export const dbUrl =
  "mongodb+srv://sarojamit4956:<EMAIL>/";

/**
 * List of supported blockchain RPC endpoints.
 * @type {{ name: string, url: string }[]}
 */
// export const rpc = [
//   {
//     name: "Ethereum",
//     url: [
//       `https://mainnet.infura.io/v3/`,
//       "https://eth.llamarpc.com",
//       "https://ethereum-rpc.publicnode.com",
//       "https://rpc.mevblocker.io/noreverts",
//       "https://rpc.eth.gateway.fm",
//       "https://rpc.flashbots.net",
//       "https://core.gashawk.io/rpc",
//       "https://rpc.flashbots.net",
//       "https://mainnet.gateway.tenderly.co",
//       "https://eth.nodeconnect.org",
//       "https://rpc.flashbots.net",
//       "https://mainnet.optimism.io",
//       "https://rpc-mainnet.kcc.network",
//       "https://rpc.flashbots.net",
//       "https://eth-mainnet.public.blastapi.io",
//       "https://bsc-dataseed.binance.org",
//       "https://bsc-dataseed1.binance.org",
//       "https://bsc-dataseed1.defibit.io",
//       "https://bsc-dataseed.binance.org",
//       "https://bsc-dataseed1.ninicoin.io",
//       "https://rpc-mainnet.matic.quiknode.pro",
//       "https://mainnet.optimism.io",
//       "https://arb1.arbitrum.io/rpc",
//       "https://api.avax.network/ext/bc/C/rpc",
//       "https://rpcapi.fantom.network",
//       "https://rpc.moonriver.moonbeam.network",
//       "https://rpc.gnosischain.com",
//       "https://api.harmony.one",
//       "https://forno.celo.org",
//       "https://mainnet.aurora.dev",
//       "https://rpc.fuse.io",
//       "https://evm-cronos.crypto.org",
//       "https://evmexplorer.velas.com/rpc",
//       "https://rpc.meter.io",
//       "https://rpc.api.moonbeam.network",
//       "https://andromeda.metis.io/?owner=1088",
//       "https://rpc.c1.milkomeda.com:8545",
//       "https://mainnet.telos.net/evm",
//       "https://exchainrpc.okex.org",
//       "https://rpc.astar.network",
//       "https://emerald.oasis.dev",
//       "https://eth-rpc-api.thetatoken.org/rpc",
//       "https://gwan-ssl.wandevs.org:56891",
//       "https://rpc.bittorrentchain.io",
//       "https://rpctest.meter.io",
//       "https://eth-rpc-api-testnet.thetatoken.org/rpc",
//       "https://rpc.shiden.astar.network",
//       "https://rpc.api.moonbeam.network",
//       "https://evm-cronos.crypto.org",
//       "https://mainnet.aurora.dev",
//       "https://babel-api.mainnet.iotex.io",
//       "https://rpc.meter.io",
//       "https://rpc.syscoin.org",
//       "https://rpc.api.moonbeam.network",
//       "https://rpc-mainnet.findora.org",
//       "https://eth-rpc-karura.aca-api.network",
//       "https://mainnet.boba.network",
//       "https://api.harmony.one",
//       "https://rpc.fuse.io",
//       "https://forno.celo.org",
//       "https://mainnet-rpc.thundercore.com",
//       "https://api.harmony.one",
//       "https://evm.confluxrpc.com",
//     ],
//   },
//   // {
//   //   name: "Binance Smart Chain",
//   //   url: "https://bsc-dataseed.binance.org/",
//   // }
// ];

/**
 * List of API keys for accessing blockchain services.
 * @type {string[]}
 */
export const apiKey: string[] = [
  "e1e4bfdbe8ce4af5a636dd00c6ea7f11",
  "e7e203bc4dca40ed8d2f908259312c15",
  "5b55d0dfb4c84e27931060c6a5d8947b",
  "9e07875428a04ef2abff735122a70a62",
  "3a28dbaa9c8d4b8d8c5d0fb46858be69",
  "b3b1eb23654b49fe827d3acce3a9398c",
  "bec63ab241fc43df8069bdb31fc224b9",
];
