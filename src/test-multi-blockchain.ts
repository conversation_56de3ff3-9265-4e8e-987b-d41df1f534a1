import { generateMultiBlockchainWalletsFromSeed, BLOCKCHAIN_CONFIG } from "./walletService/SeedPhase";

/**
 * Test script to verify multi-blockchain wallet generation
 */
async function testMultiBlockchainWallets() {
  console.log("🚀 Testing Multi-Blockchain Wallet Generation...\n");

  try {
    // Generate wallets for multiple blockchains from a single seed phrase
    const { mnemonic, walletsByBlockchain } = generateMultiBlockchainWalletsFromSeed();

    console.log("📝 Generated Mnemonic:", mnemonic);
    console.log("=" .repeat(80));

    // Display wallets for each blockchain type
    Object.entries(walletsByBlockchain).forEach(([blockchainKey, blockchainData]: [string, any]) => {
      const config = blockchainData.config;
      const wallets = blockchainData.wallets;

      console.log(`\n🔗 ${config.name} (${config.addressType})`);
      console.log(`   Derivation Path: ${config.derivationPath}`);
      console.log(`   Coin Type: ${config.coinType}`);
      console.log("   Wallets:");

      wallets.slice(0, 3).forEach((wallet: any, index: number) => {
        console.log(`     ${index}: ${wallet.address}`);
        console.log(`        Private Key: ${wallet.privateKey.substring(0, 20)}...`);
        console.log(`        Full Path: ${wallet.derivationPath}`);
      });

      if (wallets.length > 3) {
        console.log(`     ... and ${wallets.length - 3} more wallets`);
      }
    });

    console.log("\n" + "=" .repeat(80));
    console.log("✅ Multi-blockchain wallet generation completed successfully!");
    console.log(`📊 Generated wallets for ${Object.keys(walletsByBlockchain).length} blockchain types`);
    
    // Show blockchain configuration
    console.log("\n🔧 Available Blockchain Configurations:");
    Object.entries(BLOCKCHAIN_CONFIG).forEach(([key, config]) => {
      console.log(`   ${key}: ${config.name} (Coin Type: ${config.coinType})`);
    });

  } catch (error) {
    console.error("❌ Error testing multi-blockchain wallets:", error);
  }
}

// Run the test
testMultiBlockchainWallets();
