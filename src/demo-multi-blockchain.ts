import { WalletManager } from "./walletService";

/**
 * <PERSON><PERSON> script to show multi-blockchain wallet checking in action
 */
async function demoMultiBlockchainWalletChecking() {
  console.log("🚀 Starting Multi-Blockchain Wallet Demo...\n");

  try {
    // Initialize WalletManager with a test network (Ethereum mainnet)
    const walletManager = new WalletManager(
      "https://eth.llamarpc.com", // Free Ethereum RPC
      {
        name: "Ethereum Mainnet",
        chainId: 1,
        rpcUrl: "https://eth.llamarpc.com"
      },
      1 // Single thread for demo
    );

    console.log("📡 Connected to Ethereum Mainnet");
    console.log("🔍 Starting multi-blockchain wallet generation and checking...");
    console.log("⏰ This will run continuously. Press Ctrl+C to stop.\n");

    // Start the multi-blockchain process
    // This will:
    // 1. Generate seed phrases
    // 2. Create wallets for multiple blockchain types
    // 3. Check balances on the current network
    // 4. Store results in MongoDB
    await walletManager.mainMultiBlockchain();

  } catch (error) {
    console.error("❌ Error in demo:", error);
  }
}

/**
 * <PERSON><PERSON> script to show single blockchain (original) functionality
 */
async function demoSingleBlockchainWalletChecking() {
  console.log("🚀 Starting Single Blockchain (Original) Wallet Demo...\n");

  try {
    // Initialize WalletManager with a test network
    const walletManager = new WalletManager(
      "https://eth.llamarpc.com", // Free Ethereum RPC
      {
        name: "Ethereum Mainnet",
        chainId: 1,
        rpcUrl: "https://eth.llamarpc.com"
      },
      1 // Single thread for demo
    );

    console.log("📡 Connected to Ethereum Mainnet");
    console.log("🔍 Starting original wallet generation and checking...");
    console.log("⏰ This will run continuously. Press Ctrl+C to stop.\n");

    // Start the original process
    await walletManager.main();

  } catch (error) {
    console.error("❌ Error in demo:", error);
  }
}

// Check command line arguments to determine which demo to run
const args = process.argv.slice(2);
const mode = args[0] || "multi";

if (mode === "multi") {
  console.log("🌐 Running Multi-Blockchain Demo");
  demoMultiBlockchainWalletChecking();
} else if (mode === "single") {
  console.log("🔗 Running Single Blockchain Demo");
  demoSingleBlockchainWalletChecking();
} else {
  console.log("Usage:");
  console.log("  npm run demo:multi   - Run multi-blockchain demo");
  console.log("  npm run demo:single  - Run single blockchain demo");
  console.log("");
  console.log("Or directly:");
  console.log("  node dist/demo-multi-blockchain.js multi");
  console.log("  node dist/demo-multi-blockchain.js single");
}
