[{"name": "Ethereum Mainnet", "chainId": 1, "url": ["https://mainnet.infura.io/v3/", "https://eth.llamarpc.com", "https://ethereum-rpc.publicnode.com", "https://rpc.mevblocker.io/noreverts", "https://rpc.eth.gateway.fm", "https://rpc.flashbots.net", "https://core.gashawk.io/rpc", "https://rpc.flashbots.net", "https://mainnet.gateway.tenderly.co", "https://eth.nodeconnect.org", "https://rpc.flashbots.net", "https://mainnet.optimism.io", "https://rpc-mainnet.kcc.network", "https://rpc.flashbots.net", "https://eth-mainnet.public.blastapi.io", "https://bsc-dataseed.binance.org", "https://bsc-dataseed1.binance.org", "https://bsc-dataseed1.defibit.io", "https://bsc-dataseed.binance.org", "https://bsc-dataseed1.ninicoin.io", "https://rpc-mainnet.matic.quiknode.pro", "https://mainnet.optimism.io", "https://arb1.arbitrum.io/rpc", "https://api.avax.network/ext/bc/C/rpc", "https://rpcapi.fantom.network", "https://rpc.moonriver.moonbeam.network", "https://rpc.gnosischain.com", "https://api.harmony.one", "https://forno.celo.org", "https://mainnet.aurora.dev", "https://rpc.fuse.io", "https://evm-cronos.crypto.org", "https://evmexplorer.velas.com/rpc", "https://rpc.meter.io", "https://rpc.api.moonbeam.network", "https://andromeda.metis.io/?owner=1088", "https://rpc.c1.milkomeda.com:8545", "https://mainnet.telos.net/evm", "https://exchainrpc.okex.org", "https://rpc.astar.network", "https://emerald.oasis.dev", "https://eth-rpc-api.thetatoken.org/rpc", "https://gwan-ssl.wandevs.org:56891", "https://rpc.bittorrentchain.io", "https://rpctest.meter.io", "https://eth-rpc-api-testnet.thetatoken.org/rpc", "https://rpc.shiden.astar.network", "https://rpc.api.moonbeam.network", "https://evm-cronos.crypto.org", "https://mainnet.aurora.dev", "https://babel-api.mainnet.iotex.io", "https://rpc.meter.io", "https://rpc.syscoin.org", "https://rpc.api.moonbeam.network", "https://rpc-mainnet.findora.org", "https://eth-rpc-karura.aca-api.network", "https://mainnet.boba.network", "https://api.harmony.one", "https://rpc.fuse.io", "https://forno.celo.org", "https://mainnet-rpc.thundercore.com", "https://api.harmony.one", "https://evm.confluxrpc.com"]}, {"name": "Expanse Network", "chainId": 2, "url": ["https://node.expanse.tech"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "chainId": 7, "url": ["https://rpc.dome.cloud", "https://rpc.thaichain.org"]}, {"name": "Ubiq", "chainId": 8, "url": ["https://rpc.octano.dev", "https://pyrus2.ubiqscan.io"]}, {"name": "Optimism", "chainId": 10, "url": ["https://mainnet.optimism.io", "https://optimism-mainnet.public.blastapi.io", "https://rpc.optimism.gateway.fm"]}, {"name": "Metadium Mainnet", "chainId": 11, "url": ["https://api.metadium.com/prod"]}, {"name": "<PERSON><PERSON>e <PERSON>", "chainId": 14, "url": ["https://flare-api.flare.network/ext/C/rpc", "https://flare.rpc.thirdweb.com", "https://flare-bundler.etherspot.io", "https://rpc.ankr.com/flare", "https://01-gravelines-003-01.rpc.tatum.io/ext/bc/C/rpc", "https://01-vinthill-003-02.rpc.tatum.io/ext/bc/C/rpc", "https://rpc.au.cc/flare", "https://flare.enosys.global/ext/C/rpc", "https://flare.solidifi.app/ext/C/rpc"]}, {"name": "Diode Prenet", "chainId": 15, "url": ["https://prenet.diode.io:8443/"]}, {"name": "ThaiChain 2.0 ThaiFi", "chainId": 17, "url": ["https://rpc.thaifi.com"]}, {"name": "Songbird Canary-Network", "chainId": 19, "url": ["https://songbird-api.flare.network/ext/C/rpc", "https://01-gravelines-006-01.rpc.tatum.io/ext/bc/C/rpc", "https://01-vinthill-006-02.rpc.tatum.io/ext/bc/C/rpc", "https://02-tokyo-006-03.rpc.tatum.io/ext/bc/C/rpc", "https://rpc.au.cc/songbird", "https://songbird.enosys.global/ext/C/rpc", "https://songbird.solidifi.app/ext/C/rpc"]}, {"name": "Elastos Smart Chain", "chainId": 20, "url": ["https://api.elastos.io/eth"]}, {"name": "Kardia<PERSON><PERSON><PERSON>net", "chainId": 24, "url": ["https://rpc.kardiachain.io"]}, {"name": "Cronos Mainnet Beta", "chainId": 25, "url": ["https://evm.cronos.org", "https://evm-cronos.crypto.org"]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chainId": 27, "url": ["https://rpc.shibchain.org"]}, {"name": "Genesis L1", "chainId": 29, "url": ["https://rpc.genesisl1.org"]}, {"name": "Rootstock Mainnet", "chainId": 30, "url": ["https://public-node.rsk.co", "https://mycrypto.rsk.co"]}, {"name": "GoodData Mainnet", "chainId": 33, "url": ["https://rpc.goodata.io"]}, {"name": "SecureChain Mainnet", "chainId": 34, "url": ["https://mainnet-rpc.scai.network"]}, {"name": "TBWG Chain", "chainId": 35, "url": ["https://rpc.tbwg.io"]}, {"name": "Dxchain Mainnet", "chainId": 36, "url": ["https://mainnet.dxchain.com"]}, {"name": "Xpla Mainnet", "chainId": 37, "url": ["https://dimension-evm-rpc.xpla.dev"]}, {"name": "Valorbit", "chainId": 38, "url": ["https://rpc.valorbit.com/v2"]}, {"name": "U2U Solaris Mainnet", "chainId": 39, "url": ["https://rpc-mainnet.u2u.xyz"]}, {"name": "Telos EVM Mainnet", "chainId": 40, "url": ["https://rpc.telos.net", "https://telos.drpc.org"]}, {"name": "LUKSO Mainnet", "chainId": 42, "url": ["https://rpc.mainnet.lukso.network"]}, {"name": "Crab Network", "chainId": 44, "url": ["https://crab-rpc.darwinia.network", "https://crab-rpc.dcdao.box"]}, {"name": "Darwinia Network", "chainId": 46, "url": ["https://rpc.darwinia.network", "https://darwinia-rpc.dcdao.box", "https://darwinia-rpc.dwellir.com", "https://darwinia.rpc.subquery.network/public"]}, {"name": "Acria <PERSON>liChain", "chainId": 47, "url": ["https://aic.acria.ai"]}, {"name": "Ennothem Mainnet Proterozoic", "chainId": 48, "url": ["https://rpc.etm.network"]}, {"name": "XDC Network", "chainId": 50, "url": ["https://erpc.xinfin.network", "https://rpc.xinfin.network", "https://rpc1.xinfin.network", "https://rpc.xdcrpc.com", "https://erpc.xdcrpc.com", "https://rpc.ankr.com/xdc", "https://rpc.xdc.org"]}, {"name": "XDC Apothem Network", "chainId": 51, "url": ["https://rpc.apothem.network", "https://erpc.apothem.network", "https://apothem.xdcrpc.com"]}, {"name": "CoinEx Smart Chain Mainnet", "chainId": 52, "url": ["https://rpc.coinex.net"]}, {"name": "Openpiece Mainnet", "chainId": 54, "url": ["https://mainnet.openpiece.io"]}, {"name": "Zyx Mainnet", "chainId": 55, "url": ["https://rpc-1.zyx.network/", "https://rpc-2.zyx.network/", "https://rpc-3.zyx.network/", "https://rpc-4.zyx.network/", "https://rpc-5.zyx.network/", "https://rpc-6.zyx.network/"]}, {"name": "BNB Smart Chain Mainnet", "chainId": 56, "url": ["https://bsc-dataseed.binance.org", "https://bsc-dataseed1.binance.org", "https://bsc-dataseed2.binance.org", "https://bsc-dataseed3.binance.org", "https://bsc-dataseed4.binance.org", "https://bsc-dataseed1.defibit.io", "https://bsc-dataseed2.defibit.io", "https://bsc-dataseed1.ninicoin.io"]}, {"name": "Syscoin Mainnet", "chainId": 57, "url": ["https://rpc.syscoin.org", "https://syscoin.public-rpc.com", "https://syscoin-evm.publicnode.com"]}, {"name": "Ontology Mainnet", "chainId": 58, "url": ["http://dappnode1.ont.io:20339", "http://dappnode2.ont.io:20339", "http://dappnode3.ont.io:20339", "http://dappnode4.ont.io:20339", "https://dappnode1.ont.io:10339", "https://dappnode2.ont.io:10339", "https://dappnode3.ont.io:10339", "https://dappnode4.ont.io:10339"]}, {"name": "EOS EVM Legacy", "chainId": 59, "url": ["https://api.eosargentina.io"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "chainId": 60, "url": ["https://rpc.gochain.io"]}, {"name": "Ethereum Classic", "chainId": 61, "url": ["https://etc.rivet.link", "https://besu-at.etc-network.info", "https://geth-at.etc-network.info", "https://etc.etcdesktop.com", "https://etc.mytokenpocket.vip"]}, {"name": "Ellaism", "chainId": 64, "url": ["https://jsonrpc.ellaism.org"]}, {"name": "OKXChain Mainnet", "chainId": 66, "url": ["https://exchainrpc.okex.org", "https://okc-mainnet.gateway.pokt.network/v1/lb/6275309bea1b320039c893ff"]}, {"name": "SoterOne Mainnet", "chainId": 68, "url": ["https://rpc.soter.one"]}, {"name": "Hoo Smart Chain", "chainId": 70, "url": ["https://http-mainnet.hoosmartchain.com", "https://http-mainnet2.hoosmartchain.com"]}, {"name": "FNCY", "chainId": 73, "url": ["https://fncy-seed1.fncy.world"]}, {"name": "IDChain Mainnet", "chainId": 74, "url": ["https://idchain.one/rpc/"]}, {"name": "Decimal Smart Chain Mainnet", "chainId": 75, "url": ["https://node.decimalchain.com/web3/", "https://node1-mainnet.decimalchain.com/web3/", "https://node2-mainnet.decimalchain.com/web3/", "https://node3-mainnet.decimalchain.com/web3/", "https://node4-mainnet.decimalchain.com/web3/"]}, {"name": "Mix", "chainId": 76, "url": ["https://rpc2.mix-blockchain.org:8647"]}, {"name": "POA Network Sokol", "chainId": 77, "url": ["https://sokol.poa.network"]}, {"name": "PrimusChain mainnet", "chainId": 78, "url": ["https://ethnode.primusmoney.com/mainnet"]}, {"name": "Zenith Mainnet", "chainId": 79, "url": ["https://dataserver-us-1.zenithchain.co/", "https://dataserver-asia-3.zenithchain.co/", "https://dataserver-asia-4.zenithchain.co/", "https://dataserver-asia-2.zenithchain.co/", "https://dataserver-asia-5.zenithchain.co/", "https://dataserver-asia-6.zenithchain.co/", "https://dataserver-asia-7.zenithchain.co/"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "chainId": 80, "url": ["https://rpc.genechain.io"]}, {"name": "Japan Open Chain Mainnet", "chainId": 81, "url": ["https://rpc-1.japanopenchain.org:8545", "https://rpc-2.japanopenchain.org:8545", "https://rpc-3.japanopenchain.org"]}, {"name": "Meter Mainnet", "chainId": 82, "url": ["https://rpc.meter.io"]}, {"name": "GateChain Mainnet", "chainId": 86, "url": ["https://evm.gatenode.cc"]}, {"name": "Nova Network", "chainId": 87, "url": ["https://connect.novanetwork.io", "https://0x57.redjackstudio.com", "https://rpc.novanetwork.io:9070"]}, {"name": "Viction", "chainId": 88, "url": ["https://rpc.viction.xyz"]}, {"name": "Garizon Stage0", "chainId": 90, "url": ["https://s0.garizon.net/rpc"]}, {"name": "Garizon Stage1", "chainId": 91, "url": ["https://s1.garizon.net/rpc"]}, {"name": "Garizon Stage2", "chainId": 92, "url": ["https://s2.garizon.net/rpc"]}, {"name": "Garizon Stage3", "chainId": 93, "url": ["https://s3.garizon.net/rpc"]}, {"name": "SwissDLT", "chainId": 94, "url": ["https://rpc.swissdlt.ch"]}, {"name": "CamDL Mainnet", "chainId": 95, "url": ["https://rpc1.camdl.gov.kh/"]}, {"name": "KUB Mainnet", "chainId": 96, "url": ["https://rpc.bitkubchain.io"]}, {"name": "Six Protocol", "chainId": 98, "url": ["https://sixnet-rpc-evm.sixprotocol.net"]}, {"name": "POA Network Core", "chainId": 99, "url": ["https://core.poa.network"]}, {"name": "Gnosis", "chainId": 100, "url": ["https://rpc.gnosischain.com", "https://rpc.gnosis.gateway.fm"]}, {"name": "EtherInc", "chainId": 101, "url": ["https://api.einc.io/jsonrpc/mainnet"]}, {"name": "WorldLand Mainnet", "chainId": 103, "url": ["https://seoul.worldland.foundation", "https://seoul2.worldland.foundation"]}, {"name": "Velas EVM Mainnet", "chainId": 106, "url": ["https://evmexplorer.velas.com/rpc", "https://explorer.velas.com/rpc"]}, {"name": "ThunderCore Mainnet", "chainId": 108, "url": ["https://mainnet-rpc.thundercore.com", "https://mainnet-rpc.thundertoken.net", "https://mainnet-rpc.thundercore.io"]}, {"name": "Shibarium", "chainId": 109, "url": ["https://www.shibrpc.com", "https://rpc.shibrpc.com", "https://shib.nownodes.io"]}, {"name": "EtherLite Chain", "chainId": 111, "url": ["https://rpc.etherlite.org"]}, {"name": "Coinbit Mainnet", "chainId": 112, "url": ["https://coinbit-rpc-mainnet.chain.sbcrypto.app"]}, {"name": "<PERSON>h<PERSON>", "chainId": 113, "url": ["https://connect.dehvo.com", "https://rpc.dehvo.com", "https://rpc1.dehvo.com", "https://rpc2.dehvo.com"]}, {"name": "Uptick Mainnet", "chainId": 117, "url": ["https://json-rpc.uptick.network"]}, {"name": "ENULS Mainnet", "chainId": 119, "url": ["https://evmapi.nuls.io", "https://evmapi2.nuls.io"]}, {"name": "Realchain Mainnet", "chainId": 121, "url": ["https://rcl-dataseed1.rclsidechain.com", "https://rcl-dataseed2.rclsidechain.com", "https://rcl-dataseed3.rclsidechain.com", "https://rcl-dataseed4.rclsidechain.com"]}, {"name": "<PERSON><PERSON>", "chainId": 122, "url": ["https://rpc.fuse.io"]}, {"name": "<PERSON><PERSON>", "chainId": 123, "url": ["https://rpc.fusespark.io"]}, {"name": "Decentralized Web Mainnet", "chainId": 124, "url": ["https://decentralized-web.tech/dw_rpc.php"]}, {"name": "OYchain Mainnet", "chainId": 126, "url": ["https://rpc.mainnet.oychain.io"]}, {"name": "Huobi ECO Chain Mainnet", "chainId": 128, "url": ["https://http-mainnet.hecochain.com"]}, {"name": "Innovator Chain", "chainId": 129, "url": ["https://rpc.innovatorchain.com"]}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 130, "url": ["https://mainnet.unichain.org", "https://unichain-rpc.publicnode.com"]}, {"name": "Namefi Chain Mainnet", "chainId": 132, "url": ["https://rpc.chain.namefi.io"]}, {"name": "iExec Sidechain", "chainId": 134, "url": ["https://bellecour.iex.ec"]}, {"name": "Deamchain Mainnet", "chainId": 136, "url": ["https://mainnet.deamchain.com"]}, {"name": "Polygon Mainnet", "chainId": 137, "url": ["https://polygon-rpc.com", "https://rpc-mainnet.matic.network", "https://matic-mainnet.chainstacklabs.com", "https://rpc-mainnet.maticvigil.com", "https://rpc-mainnet.matic.quiknode.pro", "https://matic-mainnet-full-rpc.bwarelabs.com"]}, {"name": "Defi Oracle Meta Mainnet", "chainId": 138, "url": ["https://rpc.defi-oracle.io"]}, {"name": "WoopChain Mainnet", "chainId": 139, "url": ["https://rpc.woop.ai/rpc"]}, {"name": "Eternal Mainnet", "chainId": 140, "url": ["https://mainnet.eternalcoin.io/v1"]}, {"name": "DAX CHAIN", "chainId": 142, "url": ["https://rpc.prodax.io"]}, {"name": "PHI Network v2", "chainId": 144, "url": ["https://connect.phi.network"]}, {"name": "Sonic Mainnet", "chainId": 146, "url": ["https://rpc.soniclabs.com", "https://sonic-rpc.publicnode.com"]}, {"name": "Flag Mainnet", "chainId": 147, "url": ["https://mainnet-rpc.flagscan.xyz"]}, {"name": "ShimmerEVM", "chainId": 148, "url": ["https://json-rpc.evm.shimmer.network"]}, {"name": "Redbelly Network Mainnet", "chainId": 151, "url": ["https://governors.mainnet.redbelly.network"]}, {"name": "Puppynet", "chainId": 157, "url": ["https://puppynet.shibrpc.com"]}, {"name": "Roburna Mainnet", "chainId": 158, "url": ["https://dataseed.roburna.com"]}, {"name": "Armonia Eva Chain Mainnet", "chainId": 160, "url": ["https://evascan.io/api/eth-rpc/"]}, {"name": "Lightstreams Mainnet", "chainId": 163, "url": ["https://node.mainnet.lightstreams.io"]}, {"name": "Omni", "chainId": 166, "url": ["https://mainnet.omni.network"]}, {"name": "AIOZ Network", "chainId": 168, "url": ["https://eth-dataseed.aioz.network"]}, {"name": "Manta Pacific Mainnet", "chainId": 169, "url": ["https://pacific-rpc.manta.network/http", "https://manta-pacific.drpc.org"]}, {"name": "CO2e Chain", "chainId": 171, "url": ["https://rpc.co2e.cc", "https://rpc.co2ledger.xyz"]}, {"name": "OTC", "chainId": 175, "url": ["https://rpc.otc.run"]}, {"name": "DC Mainnet", "chainId": 176, "url": ["https://rpc.dcnetio.cloud"]}, {"name": "HashKey Chain", "chainId": 177, "url": ["https://mainnet.hsk.xyz"]}, {"name": "ABEY Mainnet", "chainId": 179, "url": ["https://rpc.abeychain.com"]}, {"name": "AME Chain Mainnet", "chainId": 180, "url": ["https://node1.amechain.io/"]}, {"name": "Waterfall Network", "chainId": 181, "url": ["https://rpc.waterfall.network/"]}, {"name": "IOST Mainnet", "chainId": 182, "url": ["https://l2-mainnet.iost.io"]}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 183, "url": ["https://mainnet.ethernitychain.io"]}, {"name": "Mint Mainnet", "chainId": 185, "url": ["https://rpc.mintchain.io", "https://global.rpc.mintchain.io", "https://asia.rpc.mintchain.io"]}, {"name": "Seele Mainnet", "chainId": 186, "url": ["https://rpc.seelen.pro/"]}, {"name": "Dojima", "chainId": 187, "url": ["https://rpc-d11k.dojima.network"]}, {"name": "BMC Mainnet", "chainId": 188, "url": ["https://mainnet.bmcchain.com/"]}, {"name": "CMDAO BBQ Chain", "chainId": 190, "url": ["https://bbqchain-rpc.commudao.xyz"]}, {"name": "FileFileGo", "chainId": 191, "url": ["https://rpc.filefilego.com/rpc"]}, {"name": "Crypto Emergency", "chainId": 193, "url": ["https://cemchain.com"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "chainId": 194, "url": ["https://rpc.firachain.com"]}, {"name": "X Layer Mainnet", "chainId": 196, "url": ["https://rpc.xlayer.tech", "https://xlayerrpc.okx.com"]}, {"name": "Bitchain Mainnet", "chainId": 198, "url": ["https://rpc.bitchain.biz/"]}, {"name": "BitTorrent Chain Mainnet", "chainId": 199, "url": ["https://rpc.bt.io", "https://bittorrent.drpc.org"]}, {"name": "Arbitrum on xDai", "chainId": 200, "url": ["https://arbitrum.xdaichain.com/"]}, {"name": "Wow<PERSON><PERSON>n <PERSON>", "chainId": 203, "url": ["https://rpc.wowchain.io"]}, {"name": "opBNB Mainnet", "chainId": 204, "url": ["https://opbnb-mainnet-rpc.bnbchain.org", "https://opbnb-mainnet.nodereal.io/v1/64a9df0874fb4a93b9d0a3849de012d3", "https://opbnb-mainnet.nodereal.io/v1/e9a36765eb8a40b9bd12e680a1fd2bc5", "https://opbnb-rpc.publicnode.com", "https://opbnb.drpc.org"]}, {"name": "EKAASH", "chainId": 205, "url": ["https://mainnet.ekaash.biz"]}, {"name": "VinuChain Network", "chainId": 207, "url": ["https://vinuchain-rpc.com"]}, {"name": "Structx Mainnet", "chainId": 208, "url": ["https://mainnet.structx.io"]}, {"name": "Bitnet", "chainId": 210, "url": ["https://rpc.bitnet.money", "https://rpc.btnscan.com", "https://rpc.btn.network", "https://rpc.bitnetmoney.com", "https://rpc.btn-network.org"]}, {"name": "Freight Trust Network", "chainId": 211, "url": ["http://13.57.207.168:3435"]}, {"name": "MAPO Makalu", "chainId": 212, "url": ["https://testnet-rpc.maplabs.io"]}, {"name": "B2 Hub Mainnet", "chainId": 213, "url": ["https://hub-rpc.bsquared.network"]}, {"name": "Shinarium Mainnet", "chainId": 214, "url": ["https://mainnet.shinarium.org"]}, {"name": "IDN Mainnet", "chainId": 215, "url": ["https://dataseed1.idn-rpc.com", "https://dataseed2.idn-rpc.com", "https://dataseed3.idn-rpc.com"]}, {"name": "SiriusNet V2", "chainId": 217, "url": ["https://rpc2.siriusnet.io"]}, {"name": "SoterOne Mainnet old", "chainId": 218, "url": ["https://rpc.soter.one"]}, {"name": "BlockEx Mainnet", "chainId": 221, "url": ["https://rpc.blockex.biz"]}, {"name": "Permission", "chainId": 222, "url": ["https://blockchain-api-mainnet.permission.io/rpc"]}, {"name": "B2 Mainnet", "chainId": 223, "url": ["https://mainnet.b2-rpc.com", "https://rpc.bsquared.network", "https://b2-mainnet.alt.technology", "https://b2-mainnet-public.s.chainbase.com", "https://rpc.ankr.com/b2"]}, {"name": "LACHAIN Mainnet", "chainId": 225, "url": ["https://rpc-mainnet.lachain.io"]}, {"name": "Prom", "chainId": 227, "url": ["https://prom-rpc.eu-north-2.gateway.fm"]}, {"name": "Mind Network Mainnet", "chainId": 228, "url": ["https://rpc-mainnet.mindnetwork.xyz"]}, {"name": "SwapDEX", "chainId": 230, "url": ["https://rpc.swapdex.network"]}, {"name": "Lens", "chainId": 232, "url": ["https://rpc.lens.xyz"]}, {"name": "Blast Mainnet", "chainId": 238, "url": ["https://zkevmrpc.blastchain.org"]}, {"name": "Plinga Mainnet", "chainId": 242, "url": ["https://rpcurl.mainnet.plgchain.com", "https://rpcurl.plgchain.blockchain.evmnode.online", "https://rpcurl.mainnet.plgchain.plinga.technology"]}, {"name": "Energy Web Chain", "chainId": 246, "url": ["https://rpc.energyweb.org"]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chainId": 247, "url": ["https://rpc.choochain.io"]}, {"name": "Oasys Mainnet", "chainId": 248, "url": ["https://rpc.mainnet.oasys.games"]}, {"name": "Fantom Opera", "chainId": 250, "url": ["https://rpcapi.fantom.network", "https://rpc.ftm.tools", "https://rpc.fantom.network"]}, {"name": "Glide L1 Protocol XP", "chainId": 251, "url": ["https://rpc-api.glideprotocol.xyz/l1-rpc/"]}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 252, "url": ["https://rpc.frax.com", "https://fraxtal-rpc.publicnode.com"]}, {"name": "Glide L2 Protocol XP", "chainId": 253, "url": ["https://rpc-api.glideprotocol.xyz/l2-rpc/"]}, {"name": "Swan Chain Mainnet", "chainId": 254, "url": ["https://mainnet-rpc.swanchain.org", "https://mainnet-rpc-01.swanchain.org", "https://mainnet-rpc-02.swanchain.org", "https://mainnet-rpc-03.swanchain.org", "https://mainnet-rpc-04.swanchain.org"]}, {"name": "Kroma", "chainId": 255, "url": ["https://api.kroma.network", "https://rpc-kroma.rockx.com"]}, {"name": "Neonlink Mainnet", "chainId": 259, "url": ["https://mainnet.neonlink.io"]}, {"name": "Guru Network", "chainId": 260, "url": ["https://rpc-main.gurunetwork.ai"]}, {"name": "SUR Blockchain Network", "chainId": 262, "url": ["https://sur.nilin.org"]}, {"name": "High Performance Blockchain", "chainId": 269, "url": ["https://hpbnode.com"]}, {"name": "EgonCoin Mainnet", "chainId": 271, "url": ["https://rpc.egonscan.com"]}, {"name": "XR One", "chainId": 273, "url": ["https://xr1.calderachain.xyz/http"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "chainId": 274, "url": ["https://rpc1.mainnet.lachain.network", "https://rpc2.mainnet.lachain.network", "https://lachain.rpc-nodes.cedalio.dev"]}, {"name": "xFair.AI Mainnet", "chainId": 278, "url": ["https://rpc_mainnet.xfair.ai"]}, {"name": "BPX Chain", "chainId": 279, "url": ["https://rpc.bpxchain.cc"]}, {"name": "Boba Network", "chainId": 288, "url": ["https://mainnet.boba.network", "https://replica.boba.network", "https://boba-ethereum.gateway.tenderly.co", "https://gateway.tenderly.co/public/boba-ethereum", "https://boba-eth.drpc.org"]}, {"name": "Orderly Mainnet", "chainId": 291, "url": ["https://rpc.orderly.network"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "chainId": 293, "url": ["https://rpc.davinci.bz"]}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 295, "url": ["https://mainnet.hashio.io/api", "https://hedera.linkpool.pro"]}, {"name": "Bobaopera", "chainId": 301, "url": ["https://bobaopera.boba.network", "https://replica.bobaopera.boba.network"]}, {"name": "ZKSats Mainnet", "chainId": 305, "url": ["https://mainnet.zksats.io"]}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 308, "url": ["https://rpc.furtheon.org"]}, {"name": "Omax Mainnet", "chainId": 311, "url": ["https://mainapi.omaxray.com", "https://mainapi.omaxscan.com"]}, {"name": "Neurochain Mainnet", "chainId": 313, "url": ["https://nc-rpc-prd1.neurochain.io", "https://nc-rpc-prd2.neurochain.io"]}, {"name": "Filecoin - Mainnet", "chainId": 314, "url": ["https://api.node.glif.io/", "https://rpc.ankr.com/filecoin", "https://filecoin-mainnet.chainstacklabs.com/rpc/v1", "https://filfox.info/rpc/v1", "https://filecoin.drpc.org"]}, {"name": "WorldEcoMoney", "chainId": 315, "url": ["https://rpc.wemblockchain.com"]}, {"name": "ZKcandy Mainnet", "chainId": 320, "url": ["https://rpc.zkcandy.io"]}, {"name": "KCC Mainnet", "chainId": 321, "url": ["https://rpc-mainnet.kcc.network", "https://kcc.mytokenpocket.vip", "https://public-rpc.blockpi.io/http/kcc"]}, {"name": "Cosvm Mainnet", "chainId": 323, "url": ["https://rpc.cosvm.net"]}, {"name": "zkSync Mainnet", "chainId": 324, "url": ["https://mainnet.era.zksync.io", "https://zksync.drpc.org"]}, {"name": "GRVT Exchange", "chainId": 325, "url": ["https://rpc.grvt.io"]}, {"name": "Web3Q Mainnet", "chainId": 333, "url": ["https://mainnet.web3q.io:8545"]}, {"name": "<PERSON>den", "chainId": 336, "url": ["https://shiden.api.onfinality.io/public", "https://shiden-rpc.dwellir.com", "https://shiden.public.blastapi.io"]}, {"name": "R5 Network", "chainId": 337, "url": ["https://rpc.r5.network"]}, {"name": "TSC Mainnet", "chainId": 345, "url": ["https://rpc01.trias.one"]}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 360, "url": ["https://mainnet.shape.network", "https://shape-mainnet.g.alchemy.com/public"]}, {"name": "Theta Mainnet", "chainId": 361, "url": ["https://eth-rpc-api.thetatoken.org/rpc"]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chainId": 369, "url": ["https://rpc.pulsechain.com", "https://pulsechain-rpc.publicnode.com", "https://rpc-pulsechain.g4mm4.io"]}, {"name": "ZKAmoeba Mainnet", "chainId": 381, "url": ["https://rpc.mainnet.zkamoeba.com/rpc"]}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 385, "url": ["https://rpc-bitfalls1.lisinski.online"]}, {"name": "Cronos zkEVM Mainnet", "chainId": 388, "url": ["https://mainnet.zkevm.cronos.org"]}, {"name": "NEAR Protocol", "chainId": 397, "url": ["https://eth-rpc.mainnet.near.org"]}, {"name": "Nativ3 Mainnet", "chainId": 399, "url": ["https://rpc.nativ3.network"]}, {"name": "Syndr L3", "chainId": 404, "url": ["https://rpc.syndr.com"]}, {"name": "Pepe Chain Mainnet", "chainId": 411, "url": ["https://rpc.pepe-chain.vip"]}, {"name": "SX Network Mainnet", "chainId": 416, "url": ["https://rpc.sx.technology"]}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 422, "url": ["https://mainnet-rpc.vrd.network"]}, {"name": "PGN (Public Goods Network)", "chainId": 424, "url": ["https://rpc.publicgoods.network"]}, {"name": "Stenix Mainnet", "chainId": 425, "url": ["https://stenix.network/pub"]}, {"name": "The Widows Mite", "chainId": 426, "url": ["https://rpc.twmcrypto.com/"]}, {"name": "Zeeth Chain", "chainId": 427, "url": ["https://rpc.zeeth.io"]}, {"name": "Geso Verse", "chainId": 428, "url": ["https://rpc.verse.gesoten.com/"]}, {"name": "Boyaa Mainnet", "chainId": 434, "url": ["https://evm-rpc.mainnet.boyaa.network"]}, {"name": "ARZIO Chain", "chainId": 456, "url": ["https://chain-rpc.arzio.co"]}, {"name": "Areon Network Mainnet", "chainId": 463, "url": ["https://mainnet-rpc.areon.network", "https://mainnet-rpc2.areon.network", "https://mainnet-rpc3.areon.network", "https://mainnet-rpc4.areon.network", "https://mainnet-rpc5.areon.network"]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chainId": 466, "url": ["https://rpc.appchain.xyz/http"]}, {"name": "Form Network", "chainId": 478, "url": ["https://rpc.form.network/http"]}, {"name": "World Chain", "chainId": 480, "url": ["https://worldchain-mainnet.g.alchemy.com/public", "https://480.rpc.thirdweb.com", "https://worldchain-mainnet.gateway.tenderly.co"]}, {"name": "BlackFort Exchange Network", "chainId": 488, "url": ["https://rpc.blackfort.network/mainnet/rpc"]}, {"name": "Landstars", "chainId": 495, "url": ["https://13882-60301.pph-server.de"]}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 499, "url": ["https://rpc.rupaya.io"]}, {"name": "Camino C-Chain", "chainId": 500, "url": ["https://api.camino.network/ext/bc/C/rpc"]}, {"name": "Syndicate Chain", "chainId": 510, "url": ["https://rpc-mainnet.syndicate.io"]}, {"name": "Double-A Chain Mainnet", "chainId": 512, "url": ["https://rpc.acuteangle.com"]}, {"name": "Gear Zero Network Mainnet", "chainId": 516, "url": ["https://gzn.linksme.info"]}, {"name": "XT Smart Chain Mainnet", "chainId": 520, "url": ["https://datarpc1.xsc.pub", "https://datarpc2.xsc.pub", "https://datarpc3.xsc.pub"]}, {"name": "Firechain Mainnet", "chainId": 529, "url": ["https://rpc-mainnet.thefirechain.com", "https://rpc-mainnet.firestation.io"]}, {"name": "Pundi AIFX Omnilayer", "chainId": 530, "url": ["https://fx-json-web3.functionx.io:8545"]}, {"name": "Candle", "chainId": 534, "url": ["https://candle-rpc.com/", "https://rpc.cndlchain.com"]}, {"name": "OpTrust Mainnet", "chainId": 537, "url": ["https://rpc.optrust.io"]}, {"name": "River", "chainId": 550, "url": ["https://mainnet.rpc.river.build", "https://towns-mainnet.calderachain.xyz/http"]}, {"name": "Vela1 Chain Mainnet", "chainId": 555, "url": ["https://rpc.velaverse.io"]}, {"name": "Tao Network", "chainId": 558, "url": ["https://rpc.testnet.tao.network", "http://rpc.testnet.tao.network:8545", "https://rpc.tao.network"]}, {"name": "Rollux Mainnet", "chainId": 570, "url": ["https://rpc.rollux.com", "https://rpc.ankr.com/rollux"]}, {"name": "MetaChain <PERSON>net", "chainId": 571, "url": ["https://rpc.metatime.com"]}, {"name": "Filenova Mainnet", "chainId": 579, "url": ["https://rpc.filenova.org"]}, {"name": "Astar", "chainId": 592, "url": ["https://rpc.astar.network:8545"]}, {"name": "EIOB Mainnet", "chainId": 612, "url": ["https://rpc.eiob.xyz"]}, {"name": "Graphlinq Blockchain Mainnet", "chainId": 614, "url": ["https://glq-dataseed.graphlinq.io"]}, {"name": "Skynet", "chainId": 619, "url": ["http://rpc.skynet.io"]}, {"name": "Binary Mainnet", "chainId": 624, "url": ["https://rpc.zero.thebinaryholdings.com"]}, {"name": "NFB Chain", "chainId": 632, "url": ["https://node.nfbchain.com"]}, {"name": "Avocado", "chainId": 634, "url": ["https://rpc.avocado.instadapp.io"]}, {"name": "Endurance Smart Chain Mainnet", "chainId": 648, "url": ["https://rpc-endurance.fusionist.io/"]}, {"name": "Kali<PERSON><PERSON>", "chainId": 654, "url": ["https://mainnet.kalichain.com"]}, {"name": "AmaxSmartchain", "chainId": 662, "url": ["https://rpc.amaxchain.io"]}, {"name": "LAOS Arrakis", "chainId": 667, "url": ["https://arrakis.gorengine.com/own"]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chainId": 668, "url": ["https://rpc.juncachain.com"]}, {"name": "Janction", "chainId": 678, "url": ["https://rpc.janction.io"]}, {"name": "Karura Network", "chainId": 686, "url": ["https://eth-rpc-karura.aca-api.network"]}, {"name": "Redstone", "chainId": 690, "url": ["https://rpc.redstonechain.com"]}, {"name": "<PERSON><PERSON>", "chainId": 698, "url": ["https://rpc.matchain.io"]}, {"name": "BlockChain Station Mainnet", "chainId": 707, "url": ["https://rpc-mainnet.bcsdev.io"]}, {"name": "Highbury", "chainId": 710, "url": ["https://highbury.furya.io", "https://rest.furya.io"]}, {"name": "Tucana", "chainId": 711, "url": ["https://evm-rpc.tucana.zone"]}, {"name": "Birdee-2", "chainId": 712, "url": ["https://evm-rpc.birdee-2.tucana.zone"]}, {"name": "Vrcscan Mainnet", "chainId": 713, "url": ["https://rpc-mainnet-5.vrcscan.com", "https://rpc-mainnet-6.vrcscan.com", "https://rpc-mainnet-7.vrcscan.com", "https://rpc-mainnet-8.vrcscan.com"]}, {"name": "UXLINK ONE Mainnet", "chainId": 718, "url": ["https://rpc.uxlinkone.com"]}, {"name": "Shibarium Beta", "chainId": 719, "url": ["https://puppynet.shibrpc.com"]}, {"name": "Lycan Chain", "chainId": 721, "url": ["https://rpc.lycanchain.com/", "https://us-east.lycanchain.com", "https://us-west.lycanchain.com", "https://eu-north.lycanchain.com", "https://eu-west.lycanchain.com", "https://asia-southeast.lycanchain.com"]}, {"name": "<PERSON><PERSON>", "chainId": 727, "url": ["https://data.bluchain.pro"]}, {"name": "Lovely Network Mainnet", "chainId": 730, "url": ["https://rpc.lovely.network"]}, {"name": "Tranched Mainnet", "chainId": 743, "url": ["https://tranched-mainnet.calderachain.xyz/http"]}, {"name": "Flow EVM Mainnet", "chainId": 747, "url": ["https://mainnet.evm.nodes.onflow.org"]}, {"name": "Rivalz", "chainId": 753, "url": ["https://rivalz.calderachain.xyz/http"]}, {"name": "QL1", "chainId": 766, "url": ["https://rpc.qom.one"]}, {"name": "cheapETH", "chainId": 777, "url": ["https://node.cheapeth.org/rpc"]}, {"name": "MAAL Chain", "chainId": 786, "url": ["https://node1-mainnet.maalscan.io/", "https://node2-mainnet.maalscan.io/", "https://node3-mainnet.maalscan.io/"]}, {"name": "Acala Network", "chainId": 787, "url": ["https://eth-rpc-acala.aca-api.network"]}, {"name": "Patex", "chainId": 789, "url": ["https://rpc.patex.io/"]}, {"name": "Lucid Block<PERSON>in", "chainId": 800, "url": ["https://rpc.lucidcoin.io"]}, {"name": "<PERSON><PERSON>", "chainId": 803, "url": ["https://orig.haichain.io/"]}, {"name": "Evoz Mainnet", "chainId": 805, "url": ["https://rpc.evozscan.com"]}, {"name": "Qitmeer Network Mainnet", "chainId": 813, "url": ["https://evm-dataseed1.meerscan.io", "https://evm-dataseed2.meerscan.io", "https://evm-dataseed3.meerscan.io", "https://evm-dataseed.meerscan.com", "https://qng.rpc.qitmeer.io", "https://mainnet.meerlabs.com", "https://rpc.dimai.ai", "https://rpc.woowow.io"]}, {"name": "Firechain zkEVM", "chainId": 814, "url": ["https://rpc-zkevm.thefirechain.com", "https://rpc-zkevm.firestation.io"]}, {"name": "BeOne Chain Mainnet", "chainId": 818, "url": ["https://dataseed1.beonechain.com", "https://dataseed2.beonechain.com", "https://dataseed-us1.beonechain.com", "https://dataseed-us2.beonechain.com", "https://dataseed-uk1.beonechain.com", "https://dataseed-uk2.beonechain.com"]}, {"name": "Callisto Mainnet", "chainId": 820, "url": ["https://rpc.callistodao.org"]}, {"name": "Daily Network Mainnet", "chainId": 824, "url": ["https://rpc.mainnet.dailycrypto.net"]}, {"name": "Taraxa Mainnet", "chainId": 841, "url": ["https://rpc.mainnet.taraxa.io/", "https://ws.mainnet.taraxa.io"]}, {"name": "HongKong Mainnet", "chainId": 852, "url": ["https://eth.jegotrip.net"]}, {"name": "Zeeth Chain Dev", "chainId": 859, "url": ["https://rpc.dev.zeeth.io"]}, {"name": "Electra Network", "chainId": 861, "url": ["https://rpc.electranetwork.tech"]}, {"name": "Fantasia Chain Mainnet", "chainId": 868, "url": ["https://mainnet-data1.fantasiachain.com/", "https://mainnet-data2.fantasiachain.com/", "https://mainnet-data3.fantasiachain.com/"]}, {"name": "Bandai Namco Research Verse Mainnet", "chainId": 876, "url": ["https://rpc.main.oasvrs.bnken.net"]}, {"name": "Dexit Network", "chainId": 877, "url": ["https://dxt.dexit.network"]}, {"name": "Ambros Chain Mainnet", "chainId": 880, "url": ["https://api.ambros.network"]}, {"name": "Weber Governance Mainnet", "chainId": 881, "url": ["https://chain.myweber.org"]}, {"name": "Wanchain", "chainId": 888, "url": ["https://gwan-ssl.wandevs.org:56891/"]}, {"name": "MAXI Chain Mainnet", "chainId": 899, "url": ["https://rpc.maxi.network"]}, {"name": "TAPROOT Mainnet", "chainId": 911, "url": ["https://rpc.taprootchain.io"]}, {"name": "Rinia", "chainId": 917, "url": ["https://rinia-rpc1.thefirechain.com", "https://rpc-rinia.firestation.io"]}, {"name": "SlerfChain <PERSON>net", "chainId": 918, "url": ["https://rpc.slerfchain.xyz"]}, {"name": "Yidark Chain Mainnet", "chainId": 927, "url": ["https://rpc.yidark.io"]}, {"name": "Haust Mainnet", "chainId": 938, "url": ["https://haust-network-rpc.eu-north-2.gateway.fm"]}, {"name": "Lyra Chain", "chainId": 957, "url": ["https://rpc.lyra.finance"]}, {"name": "BTC20 Smart Chain", "chainId": 963, "url": ["https://rpc.bitcoincode.technology/"]}, {"name": "Subtensor EVM", "chainId": 964, "url": ["https://lite.chain.opentensor.ai"]}, {"name": "EthXY", "chainId": 969, "url": ["https://rpc.ethxy.com"]}, {"name": "Oort Mainnet", "chainId": 970, "url": ["https://mainnet-rpc.oortech.com"]}, {"name": "<PERSON><PERSON>", "chainId": 972, "url": ["https://ascraeus-rpc.oortech.com"]}, {"name": "Palm Smart Chain", "chainId": 973, "url": ["https://rpc.palmsmartchain.io"]}, {"name": "Nepal Blockchain Network", "chainId": 977, "url": ["https://api.nepalblockchain.dev", "https://api.nepalblockchain.network"]}, {"name": "TOP Mainnet EVM", "chainId": 980, "url": ["https://ethapi.topnetwork.org"]}, {"name": "Memo Smart Chain Mainnet", "chainId": 985, "url": ["https://chain.metamemo.one:8501"]}, {"name": "LAGOM Mainnet", "chainId": 986, "url": ["https://rpc1.lagom.mainnet.zeeve.net"]}, {"name": "BinaryChain Mainnet", "chainId": 987, "url": ["https://rpc.binarychain.org"]}, {"name": "eLiberty Mainnet", "chainId": 990, "url": ["https://rpc.eliberty.ngo"]}, {"name": "5ireChain Mainnet", "chainId": 995, "url": ["https://rpc.5ire.network"]}, {"name": "Bifrost Polkadot Mainnet", "chainId": 996, "url": ["https://hk.p.bifrost-rpc.liebi.com"]}, {"name": "GTON Mainnet", "chainId": 1000, "url": ["https://rpc.gton.network/"]}, {"name": "Tectum Emission Token", "chainId": 1003, "url": ["https://rpc.softnote.com/"]}, {"name": "T-EKTA", "chainId": 1004, "url": ["https://test.ekta.io:8545"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "chainId": 1006, "url": ["https://rpc.lemonchain.io", "https://rpc.allthingslemon.io"]}, {"name": "<PERSON><PERSON>", "chainId": 1008, "url": ["https://mainnet.eurus.network/"]}, {"name": "Jumbochain Mainnet", "chainId": 1009, "url": ["https://rpcpriv.jumbochain.org", "https://rpc-datajumbo1.jumbochain.org", "https://rpc-datajumbo2.jumbochain.org", "https://rpc-datajumbo3.jumbochain.org"]}, {"name": "Evrice Network", "chainId": 1010, "url": ["https://meta.evrice.com"]}, {"name": "Rebus Classic Mainnet", "chainId": 1011, "url": ["https://apievm.rebuschain.com/rpc"]}, {"name": "<PERSON>", "chainId": 1012, "url": ["https://global.rpc.mainnet.newtonproject.org"]}, {"name": "CLV Parachain", "chainId": 1024, "url": ["https://api-para.clover.finance"]}, {"name": "BitTorrent Chain Donau", "chainId": 1029, "url": ["https://pre-rpc.bt.io"]}, {"name": "Conflux eSpace", "chainId": 1030, "url": ["https://evm.confluxrpc.com"]}, {"name": "OpenGPU Mainnet", "chainId": 1071, "url": ["https://mainnet-rpc.ogpuscan.io"]}, {"name": "Mintara Mainnet", "chainId": 1080, "url": ["https://subnets.avax.network/mintara/mainnet/rpc"]}, {"name": "Metis Andromeda Mainnet", "chainId": 1088, "url": ["https://andromeda.metis.io/?owner=1088", "https://metis.drpc.org", "https://metis-rpc.publicnode.com"]}, {"name": "Humans.ai Mainnet", "chainId": 1089, "url": ["https://jsonrpc.humans.nodestake.top", "https://humans-mainnet-evm.itrocket.net", "https://humans-evm-rpc.staketab.org:443", "https://evm.humans.stakepool.dev.br", "https://mainnet-humans-evm.konsortech.xyz", "https://evm-rpc.mainnet.humans.zone", "https://json-rpc.humans.bh.rocks", "https://evm-rpc.humans.huginn.tech"]}, {"name": "Dymension", "chainId": 1100, "url": ["https://dymension-evm.blockpi.network/v1/rpc/public", "https://dymension-evm-rpc.publicnode.com"]}, {"name": "Polygon zkEVM", "chainId": 1101, "url": ["https://zkevm-rpc.com", "https://polygon-zkevm.drpc.org"]}, {"name": "BLXq Mainnet", "chainId": 1108, "url": ["https://mainnet.blxq.org"]}, {"name": "WEMIX3.0 Mainnet", "chainId": 1111, "url": ["https://api.wemix.com"]}, {"name": "Core Blockchain Mainnet", "chainId": 1116, "url": ["https://rpc.coredao.org/", "https://rpc-core.icecreamswap.com", "https://core.drpc.org"]}, {"name": "Dogcoin Mainnet", "chainId": 1117, "url": ["https://mainnet-rpc.dogcoin.me"]}, {"name": "Taker Chain Mainnet", "chainId": 1125, "url": ["https://rpc-mainnet.taker.xyz"]}, {"name": "State<PERSON><PERSON>", "chainId": 1134, "url": ["https://rpc.statemesh.net"]}, {"name": "Lisk", "chainId": 1135, "url": ["https://rpc.api.lisk.com"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "chainId": 1139, "url": ["https://mathchain-asia.maiziqianbao.net/rpc", "https://mathchain-us.maiziqianbao.net/rpc"]}, {"name": "Symplexia Smart Chain", "chainId": 1149, "url": ["https://plex-rpc.plexfinance.us"]}, {"name": "ClubMos Mainnet", "chainId": 1188, "url": ["https://mainnet.mosscan.com"]}, {"name": "Iora Chain", "chainId": 1197, "url": ["https://dataseed.iorachain.com"]}, {"name": "Cuckoo Chain", "chainId": 1200, "url": ["https://mainnet-rpc.cuckoo.network"]}, {"name": "World Trade Technical Chain Mainnet", "chainId": 1202, "url": ["https://rpc.cadaut.com"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>(SBC)", "chainId": 1209, "url": ["https://rpc-nodes.saitascan.io"]}, {"name": "Popcateum Mainnet", "chainId": 1213, "url": ["https://dataseed.popcateum.org"]}, {"name": "Enter<PERSON>hain <PERSON>", "chainId": 1214, "url": ["https://tapi.entercoin.net/"]}, {"name": "ADF Chain", "chainId": 1215, "url": ["https://mainnet.adftechnology.com/"]}, {"name": "Exzo Network Mainnet", "chainId": 1229, "url": ["https://mainnet.exzo.technology"]}, {"name": "Ultron Mainnet", "chainId": 1231, "url": ["https://ultron-rpc.net"]}, {"name": "Step Network", "chainId": 1234, "url": ["https://rpc.step.network"]}, {"name": "ITX Mainnet", "chainId": 1235, "url": ["https://rpc.itxchain.com"]}, {"name": "ARC Mainnet", "chainId": 1243, "url": ["https://rpc-main-1.archiechain.io"]}, {"name": "OM Platform Mainnet", "chainId": 1246, "url": ["https://rpc-cnx.omplatform.com/"]}, {"name": "<PERSON><PERSON>er Mainnet", "chainId": 1248, "url": ["https://rpc.dogether.dog/"]}, {"name": "HALO Mainnet", "chainId": 1280, "url": ["https://nodes.halo.land"]}, {"name": "Moonbeam", "chainId": 1284, "url": ["https://rpc.api.moonbeam.network", "https://rpc.ankr.com/moonbeam"]}, {"name": "Moonriver", "chainId": 1285, "url": ["https://rpc.api.moonriver.moonbeam.network", "https://rpc.moonriver.moonbeam.network"]}, {"name": "Moonrock", "chainId": 1288, "url": ["https://rpc.api.moonrock.moonbeam.network"]}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 1294, "url": ["https://bobabeam.boba.network", "https://replica.bobabeam.boba.network"]}, {"name": "Argochain", "chainId": 1299, "url": ["https://rpc.devolvedai.com", "https://rpc-mainnet.devolvedai.com"]}, {"name": "Glue Mainnet", "chainId": 1300, "url": ["https://rpc.glue.net"]}, {"name": "COINZAX", "chainId": 1310, "url": ["https://rpc.coinzax.com"]}, {"name": "JaiHo Chain", "chainId": 1313, "url": ["https://rpc.jaihochain.com"]}, {"name": "Alyx Mainnet", "chainId": 1314, "url": ["https://rpc.alyxchain.com"]}, {"name": "AIA Mainnet", "chainId": 1319, "url": ["https://aia-dataseed1.aiachain.org", "https://aia-dataseed2.aiachain.org", "https://aia-dataseed3.aiachain.org", "https://aia-dataseed4.aiachain.org"]}, {"name": "Sei Network", "chainId": 1329, "url": ["https://evm-rpc.sei-apis.com"]}, {"name": "Elysium Mainnet", "chainId": 1339, "url": ["https://rpc.elysiumchain.tech", "https://rpc.elysiumchain.us"]}, {"name": "Blitz Subnet", "chainId": 1343, "url": ["https://subnets.avax.network/blitz/testnet/rpc"]}, {"name": "CIC Chain Mainnet", "chainId": 1353, "url": ["https://xapi.cicscan.com"]}, {"name": "Zafirium Mainnet", "chainId": 1369, "url": ["https://mainnet.zakumi.io"]}, {"name": "Ramestta Mainnet", "chainId": 1370, "url": ["https://blockchain.ramestta.com", "https://blockchain2.ramestta.com"]}, {"name": "<PERSON><PERSON>", "chainId": 1379, "url": ["https://rpc-api.kalarchain.tech"]}, {"name": "AmStar Mainnet", "chainId": 1388, "url": ["https://mainnet-rpc.amstarscan.com"]}, {"name": "Joseon Mainnet", "chainId": 1392, "url": ["https://rpc.modchain.net/blockchain.joseon.com/rpc"]}, {"name": "Perennial", "chainId": 1424, "url": ["https://rpc.perennial.foundation"]}, {"name": "ONINO Mainnet", "chainId": 1425, "url": ["https://rpc.onino.io"]}, {"name": "Rikeza Network Mainnet", "chainId": 1433, "url": ["https://rpc.rikscan.com"]}, {"name": "Living Assets Mainnet", "chainId": 1440, "url": ["https://beta.mainnet.livingassets.io/rpc", "https://gamma.mainnet.livingassets.io/rpc"]}, {"name": "MetaChain Istanbul", "chainId": 1453, "url": ["https://istanbul-rpc.metachain.dev"]}, {"name": "Ctex Scan Blockchain", "chainId": 1455, "url": ["https://mainnet-rpc.ctexscan.com/"]}, {"name": "ZKBase Mainnet", "chainId": 1456, "url": ["https://mainnet-rpc.zkbase.app"]}, {"name": "<PERSON><PERSON>", "chainId": 1480, "url": ["https://rpc.vana.org/"]}, {"name": "Vitruveo Mainnet", "chainId": 1490, "url": ["https://rpc.vitruveo.xyz"]}, {"name": "BEVM Canary", "chainId": 1501, "url": ["https://rpc-canary-1.bevm.io/", "https://rpc-canary-2.bevm.io/"]}, {"name": "Sherpax Mainnet", "chainId": 1506, "url": ["https://mainnet.sherpax.io/rpc"]}, {"name": "Story", "chainId": 1514, "url": ["https://mainnet.storyrpc.io"]}, {"name": "Beagle Messaging Chain", "chainId": 1515, "url": ["https://beagle.chat/eth"]}, {"name": "Datacore Smart Chain", "chainId": 1555, "url": ["https://rpc01.dscscan.io"]}, {"name": "Tenet", "chainId": 1559, "url": ["https://rpc.tenet.org", "https://tenet-evm.publicnode.com"]}, {"name": "StarCHAIN", "chainId": 1578, "url": ["https://rpc.starworksglobal.com"]}, {"name": "Reactive Mainnet", "chainId": 1597, "url": ["https://mainnet-rpc.rnk.dev"]}, {"name": "Betherance", "chainId": 1605, "url": ["https://rpc.bethscan.io"]}, {"name": "Ethereum Inscription Mainnet", "chainId": 1617, "url": ["https://rpc.etins.org"]}, {"name": "Catecoin Chain Mainnet", "chainId": 1618, "url": ["https://send.catechain.com"]}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 1620, "url": ["https://rpc.atheios.org/"]}, {"name": "Gravity Alpha Mainnet", "chainId": 1625, "url": ["https://rpc.gravity.xyz", "https://rpc.ankr.com/gravity"]}, {"name": "Pivotal Mainnet", "chainId": 1648, "url": ["https://mainnet.pivotalprotocol.com"]}, {"name": "Btachain", "chainId": 1657, "url": ["https://dataseed1.btachain.com/"]}, {"name": "LUDAN Mainnet", "chainId": 1688, "url": ["https://rpc.ludan.org/"]}, {"name": "NERO Mainnet", "chainId": 1689, "url": ["https://rpc.nerochain.io"]}, {"name": "Anytype EVM Chain", "chainId": 1701, "url": ["https://geth.anytype.io"]}, {"name": "TBSI Mainnet", "chainId": 1707, "url": ["https://rpc.blockchain.or.th"]}, {"name": "Doric Network", "chainId": 1717, "url": ["https://mainnet.doric.network"]}, {"name": "Palette Chain Mainnet", "chainId": 1718, "url": ["https://palette-rpc.com:22000"]}, {"name": "Ethpar Mainnet", "chainId": 1727, "url": ["https://rpc01.ethpar.net/"]}, {"name": "Reya Network", "chainId": 1729, "url": ["https://rpc.reya.network"]}, {"name": "Fuga Mainnet", "chainId": 1732, "url": ["https://rpc.fuga.blue"]}, {"name": "Fuga Develop", "chainId": 1734, "url": ["https://rpc-develop.fuga.blue"]}, {"name": "Metal L2", "chainId": 1750, "url": ["https://rpc.metall2.com"]}, {"name": "Party<PERSON><PERSON>n", "chainId": 1773, "url": ["https://tea.mining4people.com/rpc", "http://172.104.194.36:8545"]}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 1777, "url": ["https://rpc.gaussgang.com"]}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 1804, "url": ["https://cacib-saturn-test.francecentral.cloudapp.azure.com"]}, {"name": "Cube Chain Mainnet", "chainId": 1818, "url": ["https://http-mainnet.cube.network", "https://http-mainnet-sg.cube.network", "https://http-mainnet-us.cube.network"]}, {"name": "Ruby Smart Chain MAINNET", "chainId": 1821, "url": ["https://mainnet-data.rubychain.io/", "https://mainnet.rubychain.io/"]}, {"name": "PlayBlock", "chainId": 1829, "url": ["https://rpc.playblock.io"]}, {"name": "Swisstronik Mainnet", "chainId": 1848, "url": ["https://json-rpc.mainnet.swisstronik.com/unencrypted/", "https://json-rpc.mainnet.swisstronik.com"]}, {"name": "HighOctane Subnet", "chainId": 1853, "url": ["https://subnets.avax.network/highoctane/mainnet/rpc"]}, {"name": "Teslafunds", "chainId": 1856, "url": ["https://tsfapi.europool.me"]}, {"name": "Soneium", "chainId": 1868, "url": ["https://rpc.soneium.org"]}, {"name": "Whitechain", "chainId": 1875, "url": ["https://rpc.whitechain.io"]}, {"name": "Lightlink Phoenix Mainnet", "chainId": 1890, "url": ["https://replicator.phoenix.lightlink.io/rpc/v1"]}, {"name": "BON Network", "chainId": 1898, "url": ["http://rpc.boyanet.org:8545"]}, {"name": "ReDeFi Layer 2", "chainId": 1899, "url": ["https://layer2.redefi.world"]}, {"name": "Sports Chain Network", "chainId": 1904, "url": ["https://rpc.sportschainnetwork.xyz/"]}, {"name": "Bitcichain Mainnet", "chainId": 1907, "url": ["https://rpc.bitci.com"]}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 1909, "url": ["https://marklechain-rpc.merklescan.com"]}, {"name": "Scalind", "chainId": 1911, "url": ["https://rpc.scalind.com"]}, {"name": "Swellchain", "chainId": 1923, "url": ["https://swell-mainnet.alt.technology", "https://rpc.ankr.com/swell"]}, {"name": "D-Chain Mainnet", "chainId": 1951, "url": ["https://mainnet.d-chain.network/ext/bc/2ZiR1Bro5E59siVuwdNuRFzqL95NkvkbzyLBdrsYR9BLSHV7H4/rpc"]}, {"name": "Selendra Network Mainnet", "chainId": 1961, "url": ["https://rpc.selendra.org", "https://rpc2.selendra.org"]}, {"name": "<PERSON>", "chainId": 1967, "url": ["https://rpc.metatime.com/eleanor"]}, {"name": "Super Smart Chain Mainnet", "chainId": 1970, "url": ["https://rpc.scschain.com"]}, {"name": "Atelier", "chainId": 1971, "url": ["https://1971.network/atlr"]}, {"name": "RedeCoin", "chainId": 1972, "url": ["https://rpc2.redecoin.eu"]}, {"name": "ONUS Chain Mainnet", "chainId": 1975, "url": ["https://rpc.onuschain.io"]}, {"name": "SatoshIE", "chainId": 1985, "url": ["http://rpc.satosh.ie"]}, {"name": "EtherGem", "chainId": 1987, "url": ["https://jsonrpc.egem.io/custom"]}, {"name": "<PERSON>", "chainId": 1989, "url": ["https://rpc.lydiacoins.com"]}, {"name": "Hubble Exchange", "chainId": 1992, "url": ["https://rpc.hubble.exchange"]}, {"name": "Ekta", "chainId": 1994, "url": ["https://main.ekta.io"]}, {"name": "Sanko", "chainId": 1996, "url": ["https://mainnet.sanko.xyz"]}, {"name": "Kyoto", "chainId": 1997, "url": ["https://rpc.kyotochain.io"]}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 2000, "url": ["https://rpc.dogechain.dog", "https://rpc01-sg.dogechain.dog", "https://rpc.ankr.com/dogechain"]}, {"name": "Milkomeda C1 Mainnet", "chainId": 2001, "url": ["https://rpc-mainnet-cardano-evm.c1.milkomeda.com"]}, {"name": "Milkomeda A1 Mainnet", "chainId": 2002, "url": ["https://rpc-mainnet-algorand-rollup.a1.milkomeda.com"]}, {"name": "MetaLink Network", "chainId": 2004, "url": ["http://77.237.237.69:9933"]}, {"name": "Panarchy", "chainId": 2013, "url": ["https://polytopia.org:8545"]}, {"name": "MainnetZ Mainnet", "chainId": 2016, "url": ["https://mainnet-rpc.mainnetz.io", "https://eu-rpc.mainnetz.io"]}, {"name": "Adiri", "chainId": 2017, "url": ["https://rpc.telcoin.network", "https://adiri.tel", "https://node1.telcoin.network", "https://node2.telcoin.network", "https://node3.telcoin.network", "https://node4.telcoin.network"]}, {"name": "<PERSON><PERSON>", "chainId": 2020, "url": ["https://api.roninchain.com/rpc"]}, {"name": "Edgeware EdgeEVM Mainnet", "chainId": 2021, "url": ["https://edgeware-evm.jelliedowl.net", "https://edgeware-evm0.jelliedowl.net", "https://edgeware-evm1.jelliedowl.net", "https://edgeware-evm2.jelliedowl.net", "https://edgeware-evm3.jelliedowl.net"]}, {"name": "Rangers Protocol Mainnet", "chainId": 2025, "url": ["https://mainnet.rangersprotocol.com/api/jsonrpc"]}, {"name": "Edgeless Network", "chainId": 2026, "url": ["https://rpc.edgeless.network/http"]}, {"name": "Centrifuge", "chainId": 2031, "url": ["https://fullnode.centrifuge.io", "https://centrifuge-parachain.api.onfinality.io/public", "https://centrifuge-rpc.dwellir.com", "https://rpc-centrifuge.luckyfriday.io"]}, {"name": "<PERSON><PERSON>", "chainId": 2037, "url": ["https://subnets.avax.network/kiwi/testnet/rpc"]}, {"name": "Aleph Zero", "chainId": 2039, "url": ["https://rpc.alephzero-testnet.gelato.digital"]}, {"name": "<PERSON><PERSON>", "chainId": 2040, "url": ["https://rpc.vanarchain.com"]}, {"name": "NeuroWeb", "chainId": 2043, "url": ["https://astrosat.origintrail.network"]}, {"name": "Shrapnel Subnet", "chainId": 2044, "url": ["https://subnets.avax.network/shrapnel/mainnet/rpc"]}, {"name": "Stratos", "chainId": 2048, "url": ["https://web3-rpc.thestratos.org"]}, {"name": "Movo Smart Chain Mainnet", "chainId": 2049, "url": ["https://msc-rpc.movoscan.com", "https://msc-rpc.movochain.org", "https://msc-rpc.movoswap.com"]}, {"name": "Metacces Mainnet", "chainId": 2071, "url": ["https://oli.accesscan.io"]}, {"name": "Quokkacoin Mainnet", "chainId": 2077, "url": ["https://rpc.qkacoin.org"]}, {"name": "Ecoball Mainnet", "chainId": 2100, "url": ["https://api.ecoball.org/ecoball/"]}, {"name": "Exosama Network", "chainId": 2109, "url": ["https://rpc.exosama.com"]}, {"name": "UCHAIN Mainnet", "chainId": 2112, "url": ["https://rpc.uchain.link/"]}, {"name": "<PERSON>ena <PERSON>", "chainId": 2121, "url": ["https://rpc1.catenarpc.com"]}, {"name": "Metaplayerone Mainnet", "chainId": 2122, "url": ["https://rpc.metaplayer.one/"]}, {"name": "BigShortBets", "chainId": 2137, "url": ["https://market.bigsb.io"]}, {"name": "Oneness Network", "chainId": 2140, "url": ["https://rpc.onenesslabs.io/"]}, {"name": "BOSagora Mainnet", "chainId": 2151, "url": ["https://mainnet.bosagora.org", "https://rpc.bosagora.org"]}, {"name": "Findora Mainnet", "chainId": 2152, "url": ["https://rpc-mainnet.findora.org"]}, {"name": "Findora <PERSON>ge", "chainId": 2154, "url": ["https://prod-forge.prod.findora.org:8545/"]}, {"name": "Game7", "chainId": 2187, "url": ["https://mainnet-rpc.game7.io", "https://mainnet-rpc.game7.build"]}, {"name": "Snax<PERSON>hain", "chainId": 2192, "url": ["https://mainnet.snaxchain.io"]}, {"name": "Moonsama Network", "chainId": 2199, "url": ["https://rpc.moonsama.com"]}, {"name": "Antofy Mainnet", "chainId": 2202, "url": ["https://rpc.antofy.io"]}, {"name": "Bitcoin EVM", "chainId": 2203, "url": ["https://connect.bitcoinevm.com"]}, {"name": "Evanesco Mainnet", "chainId": 2213, "url": ["https://seed4.evanesco.org:8546"]}, {"name": "<PERSON><PERSON>", "chainId": 2222, "url": ["https://evm.kava.io", "https://kava-rpc.gateway.pokt.network", "https://kava-evm.rpc.thirdweb.com", "https://kava-evm-rpc.publicnode.com", "https://evm.kava-rpc.com", "https://rpc.ankr.com/kava_evm", "https://kava.drpc.org"]}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 2223, "url": ["https://bc.vcex.xyz"]}, {"name": "Krest Network", "chainId": 2241, "url": ["https://erpc-krest.peaq.network", "https://krest.unitedbloc.com"]}, {"name": "BOMB Chain", "chainId": 2300, "url": ["https://rpc.bombchain.com"]}, {"name": "Ebro Network", "chainId": 2306, "url": ["https://greendinoswap.com"]}, {"name": "CratD2C", "chainId": 2310, "url": ["https://node1.cratd2csmartchain.io", "https://node2.cratd2csmartchain.io", "https://node3.cratd2csmartchain.io"]}, {"name": "Altcoinchain", "chainId": 2330, "url": ["https://rpc0.altcoinchain.org/rpc"]}, {"name": "SOMA Network Mainnet", "chainId": 2332, "url": ["https://data-mainnet-v1.somanetwork.io/", "https://id-mainnet.somanetwork.io", "https://hk-mainnet.somanetwork.io", "https://sg-mainnet.somanetwork.io"]}, {"name": "Atleta Olympia", "chainId": 2340, "url": ["https://testnet-rpc.atleta.network", "https://rpc.ankr.com/atleta_olympia", "https://atleta-testnet.htw.tech/", "https://public-atleta.nownodes.io", "https://public-atla-testnet.fastnode.io"]}, {"name": "Omnia Chain", "chainId": 2342, "url": ["https://rpc.omniaverse.io"]}, {"name": "GOAT Network", "chainId": 2345, "url": ["https://rpc.goat.network"]}, {"name": "Silicon zkEVM", "chainId": 2355, "url": ["https://rpc.silicon.network", "https://silicon-mainnet.nodeinfra.com"]}, {"name": "TAC Turin", "chainId": 2390, "url": ["https://turin.rpc.tac.build", "https://rpc.ankr.com/tac_turin", "https://turin-ws.rpc.tac.build"]}, {"name": "TCG Verse Mainnet", "chainId": 2400, "url": ["https://rpc.tcgverse.xyz"]}, {"name": "K2 Mainnet", "chainId": 2410, "url": ["https://rpc.karak.network"]}, {"name": "XODEX", "chainId": 2415, "url": ["https://mainnet.xo-dex.com/rpc", "https://xo-dex.io"]}, {"name": "<PERSON>", "chainId": 2420, "url": ["https://rufus.calderachain.xyz/http"]}, {"name": "King Of Legends Mainnet", "chainId": 2425, "url": ["https://rpc-mainnet.kinggamer.org/"]}, {"name": "Atleta Network", "chainId": 2440, "url": ["https://rpc.mainnet.atleta.network"]}, {"name": "Hybrid Chain Network Mainnet", "chainId": 2468, "url": ["https://coredata-mainnet.hybridchain.ai/", "https://rpc-mainnet.hybridchain.ai"]}, {"name": "6Degree of Outreach", "chainId": 2477, "url": ["https://rpc.6dochain.com", "https://rpc2.6dochain.com", "https://rpc3.6dochain.com"]}, {"name": "NOW Chain Mainnet", "chainId": 2488, "url": ["https://rpc.nowscan.io"]}, {"name": "inEVM Mainnet", "chainId": 2525, "url": ["https://mainnet.rpc.inevm.com/http"]}, {"name": "Bahamut horizon", "chainId": 2552, "url": ["https://horizon-fastex-testnet.zeeve.net"]}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 2559, "url": ["https://www.kortho-chain.com"]}, {"name": "TechPay Mainnet", "chainId": 2569, "url": ["https://api.techpay.io/"]}, {"name": "Pho Blockchain Mainnet", "chainId": 2605, "url": ["https://mainnet.phochain.org"]}, {"name": "PoCRNet", "chainId": 2606, "url": ["https://pocrnet.westeurope.cloudapp.azure.com/http"]}, {"name": "Redlight Chain Mainnet", "chainId": 2611, "url": ["https://dataseed2.redlightscan.finance"]}, {"name": "EZChain C-Chain Mainnet", "chainId": 2612, "url": ["https://api.ezchain.com/ext/bc/C/rpc"]}, {"name": "AILayer Mainnet", "chainId": 2649, "url": ["https://mainnet-rpc.ailayer.xyz"]}, {"name": "K-LAOS", "chainId": 2718, "url": ["https://rpc.klaos.laosfoundation.io"]}, {"name": "Abstract", "chainId": 2741, "url": ["https://api.mainnet.abs.xyz"]}, {"name": "<PERSON><PERSON>", "chainId": 2748, "url": ["https://rpc.nanon.network"]}, {"name": "GM Network Mainnet", "chainId": 2777, "url": ["https://rpc.gmnetwork.ai"]}, {"name": "Apertum", "chainId": 2786, "url": ["https://rpc.apertum.io/ext/bc/YDJ1r9RMkewATmA7B35q1bdV18aywzmdiXwd9zGBq3uQjsCnn/rpc"]}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 2810, "url": ["https://rpc-quicknode-holesky.morphl2.io", "https://rpc-holesky.morphl2.io"]}, {"name": "Morph", "chainId": 2818, "url": ["https://rpc.morphl2.io", "https://rpc-quicknode.morphl2.io"]}, {"name": "Chips Network", "chainId": 2882, "url": ["https://node.chips.ooo/wasp/api/v1/chains/iota1pp3d3mnap3ufmgqnjsnw344sqmf5svjh26y2khnmc89sv6788y3r207a8fn/evm"]}, {"name": "Aarma Mainnet", "chainId": 2889, "url": ["https://aarmarpc.com/"]}, {"name": "Elux Chain", "chainId": 2907, "url": ["https://rpc.eluxscan.com"]}, {"name": "HYCHAIN", "chainId": 2911, "url": ["https://rpc.hychain.com/http"]}, {"name": "BitYuan Mainnet", "chainId": 2999, "url": ["https://mainnet.bityuan.com/eth"]}, {"name": "CENNZnet Nikau", "chainId": 3001, "url": ["https://nikau.centrality.me/public"]}, {"name": "Canxium Mainnet", "chainId": 3003, "url": ["https://rpc.canxium.org"]}, {"name": "PLAYA3ULL GAMES", "chainId": 3011, "url": ["https://api.mainnet.playa3ull.games"]}, {"name": "BC Hyper Chain Mainnet", "chainId": 3030, "url": ["https://mainapi.bchscan.io"]}, {"name": "Orlando Chain", "chainId": 3031, "url": ["https://rpc-testnet.orlchain.com"]}, {"name": "Bifrost Mainnet", "chainId": 3068, "url": ["https://public-01.mainnet.bifrostnetwork.com/rpc", "https://public-02.mainnet.bifrostnetwork.com/rpc"]}, {"name": "Immu3 EVM", "chainId": 3100, "url": ["https://fraa-flashbox-2800-rpc.a.stagenet.tanssi.network"]}, {"name": "Vulture EVM Beta", "chainId": 3102, "url": ["https://fraa-dancebox-3050-rpc.a.dancebox.tanssi.network"]}, {"name": "SatoshiVM Alpha Mainnet", "chainId": 3109, "url": ["https://alpha-rpc-node-http.svmscan.io"]}, {"name": "Dubxcoin network", "chainId": 3269, "url": ["https://rpcmain.arabianchain.org"]}, {"name": "EthStorage L2 Mainnet", "chainId": 3332, "url": ["http://mainnet.l2.ethstorage.io:9540"]}, {"name": "Web3Q Galileo", "chainId": 3334, "url": ["https://galileo.web3q.io:8545"]}, {"name": "peaq", "chainId": 3338, "url": ["https://peaq.api.onfinality.io/public", "https://peaq-rpc.dwellir.com", "https://peaq-rpc.publicnode.com", "https://evm.peaq.network", "https://responsive-powerful-mansion.peaq-mainnet.quiknode.pro/29963d0a2deee01a20b091926b08d68db12bc68b"]}, {"name": "EthStorage Mainnet", "chainId": 3339, "url": ["http://mainnet.ethstorage.io:9540"]}, {"name": "Pentagon Chain", "chainId": 3344, "url": ["https://rpc.pentagon.games"]}, {"name": "Meroneum", "chainId": 3366, "url": ["https://mainnet-node1.meronscan.ai/", "https://mainnet-node2.meronscan.ai/", "https://mainnet-node3.meronscan.ai/"]}, {"name": "Paribu Net Mainnet", "chainId": 3400, "url": ["https://rpc.paribu.network"]}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 3409, "url": ["https://rpc-pepe-unchained-gupg0lo9wf.t.conduit.xyz"]}, {"name": "EVOLVE Mainnet", "chainId": 3424, "url": ["https://rpc.evoexplorer.com"]}, {"name": "GTCSCAN", "chainId": 3490, "url": ["https://gtc-dataseed.gtcscan.io/"]}, {"name": "JFIN Chain", "chainId": 3501, "url": ["https://rpc.jfinchain.com"]}, {"name": "JFINPOS", "chainId": 3502, "url": ["https://rpc.jfinpos.com"]}, {"name": "PandoProject Mainnet", "chainId": 3601, "url": ["https://eth-rpc-api.pandoproject.org/rpc"]}, {"name": "Tycooncoin", "chainId": 3630, "url": ["https://mainnet-rpc.tycoscan.com"]}, {"name": "Botanix Mainnet", "chainId": 3637, "url": ["https://rpc.btxtestchain.com"]}, {"name": "iChain Network", "chainId": 3639, "url": ["https://rpc.ichainscan.com"]}, {"name": "Jouleverse Mainnet", "chainId": 3666, "url": ["https://rpc.jnsdao.com:8503"]}, {"name": "Bittex Mainnet", "chainId": 3690, "url": ["https://rpc1.bittexscan.info", "https://rpc2.bittexscan.info"]}, {"name": "Empire Network", "chainId": 3693, "url": ["https://rpc.empirenetwork.io"]}, {"name": "SenjePowers Mainnet", "chainId": 3699, "url": ["https://rpc.senjepowersscan.com"]}, {"name": "Xone Mainnet", "chainId": 3721, "url": ["https://rpc.xone.org"]}, {"name": "<PERSON><PERSON>", "chainId": 3737, "url": ["https://rpc.crossbell.io"]}, {"name": "Astar zkEVM", "chainId": 3776, "url": ["https://rpc.startale.com/astar-zkevm"]}, {"name": "AlveyChain <PERSON>", "chainId": 3797, "url": ["https://elves-core1.alvey.io", "https://elves-core2.alvey.io", "https://elves-core3.alvey.io"]}, {"name": "Firechain zkEVM Ghostrider", "chainId": 3885, "url": ["https://rpc-zkevm-ghostrider.thefirechain.com", "https://rpc-zkevm-ghostrider.firestation.io"]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chainId": 3888, "url": ["https://rpc.kalychain.io/rpc"]}, {"name": "DRAC Network", "chainId": 3912, "url": ["https://www.dracscan.com/rpc"]}, {"name": "DYNO Mainnet", "chainId": 3966, "url": ["https://api.dynoprotocol.com"]}, {"name": "PayNetwork Mainnet", "chainId": 3969, "url": ["https://rpc.paynetwork.io", "https://paynetwork-main.calderachain.xyz/http"]}, {"name": "YuanChain <PERSON>net", "chainId": 3999, "url": ["https://mainnet.yuan.org/eth"]}, {"name": "Ozone Chain Mainnet", "chainId": 4000, "url": ["https://node1.ozonechain.io"]}, {"name": "X1 Fastnet", "chainId": 4003, "url": ["https://x1-fastnet.xen.network"]}, {"name": "Bahamut ocean", "chainId": 4058, "url": ["https://rpc1.ocean.bahamutchain.com"]}, {"name": "Nahmii 3 Mainnet", "chainId": 4061, "url": ["https://rpc.n3.nahmii.io"]}, {"name": "<PERSON><PERSON>", "chainId": 4078, "url": ["https://muster.alt.technology"]}, {"name": "Zeroth Mainnet", "chainId": 4088, "url": ["https://my.zeroth.run"]}, {"name": "Bitindi Mainnet", "chainId": 4099, "url": ["https://mainnet-rpc.bitindi.org"]}, {"name": "CrossFi Mainnet", "chainId": 4158, "url": ["https://rpc.mainnet.ms/"]}, {"name": "SX Rollup", "chainId": 4162, "url": ["https://rpc.sx-rollup.gelato.digital"]}, {"name": "PHI Network V1", "chainId": 4181, "url": ["https://rpc1.phi.network", "https://rpc2.phi.network"]}, {"name": "Merlin Mainnet", "chainId": 4200, "url": ["https://rpc.merlinchain.io", "https://merlin-mainnet-enterprise.unifra.io", "https://rpc-merlin.rockx.com"]}, {"name": "Nexi Mainnet", "chainId": 4242, "url": ["https://rpc.chain.nexi.technology/", "https://chain.nexilix.com", "https://chain.nexi.evmnode.online"]}, {"name": "Nexi V2 Mainnet", "chainId": 4243, "url": ["https://chain.nexiv2.nexilix.com", "https://rpc.chainv1.nexi.technology"]}, {"name": "Echos Chain", "chainId": 4321, "url": ["https://rpc-echos-mainnet-0.t.conduit.xyz"]}, {"name": "<PERSON><PERSON>", "chainId": 4337, "url": ["https://build.onbeam.com/rpc", "https://subnets.avax.network/beam/mainnet/rpc"]}, {"name": "MemeCore", "chainId": 4352, "url": ["https://rpc.memecore.net"]}, {"name": "Credit Smart Chain Mainnet", "chainId": 4400, "url": ["https://rpc.creditsmartchain.com"]}, {"name": "Htmlcoin Mainnet", "chainId": 4444, "url": ["https://janus.htmlcoin.com/api/"]}, {"name": "Hydra Chain", "chainId": 4488, "url": ["https://rpc-mainnet.hydrachain.org"]}, {"name": "Emoney Network Mainnet", "chainId": 4545, "url": ["https://rpc-publicnode.emoney.io/", "https://public-node1-rpc.emoney.network/"]}, {"name": "TRUMPCHAIN", "chainId": 4547, "url": ["https://testnet.trumpchain.dev/http"]}, {"name": "VERY Mainnet", "chainId": 4613, "url": ["https://rpc.verylabs.io"]}, {"name": "MST Chain", "chainId": 4646, "url": ["https://mariorpc.mstblockchain.com", "https://craftrpc.mstblockchain.com"]}, {"name": "Gold Chain", "chainId": 4653, "url": ["https://chain-rpc.gold.dev"]}, {"name": "IoTeX Network Mainnet", "chainId": 4689, "url": ["https://babel-api.mainnet.iotex.io"]}, {"name": "Globel Chain", "chainId": 4893, "url": ["https://rpc.gcscan.io"]}, {"name": "OEV Network", "chainId": 4913, "url": ["https://oev.rpc.api3.org", "https://oev-network.calderachain.xyz/http"]}, {"name": "Venidium Mainnet", "chainId": 4919, "url": ["https://rpc.venidium.io"]}, {"name": "BlackFort Exchange Network Deprecated", "chainId": 4999, "url": ["https://mainnet.blackfort.network/rpc", "https://mainnet-1.blackfort.network/rpc", "https://mainnet-2.blackfort.network/rpc", "https://mainnet-3.blackfort.network/rpc"]}, {"name": "Mantle", "chainId": 5000, "url": ["https://rpc.mantle.xyz", "https://mantle-rpc.publicnode.com"]}, {"name": "Treasurenet Mainnet Alpha", "chainId": 5002, "url": ["https://node0.treasurenet.io", "https://node1.treasurenet.io", "https://node2.treasurenet.io", "https://node3.treasurenet.io"]}, {"name": "ONIGIRI Subnet", "chainId": 5040, "url": ["https://subnets.avax.network/onigiri/mainnet/rpc"]}, {"name": "Skate Mainnet", "chainId": 5050, "url": ["https://rpc.skatechain.org/"]}, {"name": "Pione Zero", "chainId": 5080, "url": ["https://rpc.zeroscan.org"]}, {"name": "Pione Chain Mainnet", "chainId": 5090, "url": ["https://rpc.pionescan.com"]}, {"name": "Syndicate Frame Chain", "chainId": 5101, "url": ["https://rpc-frame.syndicate.io"]}, {"name": "Ham", "chainId": 5112, "url": ["https://rpc.ham.fun"]}, {"name": "Bahamut", "chainId": 5165, "url": ["https://rpc1.bahamut.io", "https://rpc2.bahamut.io", "https://bahamut-rpc.publicnode.com"]}, {"name": "Smart Layer Network", "chainId": 5169, "url": ["https://rpc.main.smartlayer.network"]}, {"name": "TLChain Network Mainnet", "chainId": 5177, "url": ["https://mainnet-rpc.tlxscan.com/"]}, {"name": "EraSwap Mainnet", "chainId": 5197, "url": ["https://mainnet.eraswap.network", "https://rpc-mumbai.mainnet.eraswap.network"]}, {"name": "Humanode Mainnet", "chainId": 5234, "url": ["https://explorer-rpc-http.mainnet.stages.humanode.io"]}, {"name": "Firechain Mainnet Old", "chainId": 5290, "url": ["https://mainnet.rpc1.thefirechain.com"]}, {"name": "Uzmi Network Mainnet", "chainId": 5315, "url": ["https://network.uzmigames.com.br/"]}, {"name": "Superseed", "chainId": 5330, "url": ["https://mainnet.superseed.xyz"]}, {"name": "Netsbo", "chainId": 5333, "url": ["https://rpc1.netsbo.io", "https://rpc2.netsbo.io"]}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 5371, "url": ["https://settlus-mainnet.g.alchemy.com/public"]}, {"name": "edeXa Mainnet", "chainId": 5424, "url": ["https://rpc.edexa.network", "https://rpc.edexa.com"]}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 5433, "url": ["https://rpc.inertiascan.com"]}, {"name": "Egochain", "chainId": 5439, "url": ["https://mainnet.egochain.org"]}, {"name": "Saga", "chainId": 5464, "url": ["http://sagaevm-5464-1.jsonrpc.sagarpc.io"]}, {"name": "PointPay Mainnet", "chainId": 5511, "url": ["https://rpc-mainnet.pointpay.io"]}, {"name": "Duck<PERSON>hain <PERSON>net", "chainId": 5545, "url": ["https://rpc.duckchain.io", "https://rpc-hk.duckchain.io"]}, {"name": "Nahmii 2 Mainnet", "chainId": 5551, "url": ["https://l2.nahmii.io"]}, {"name": "Chain Verse Mainnet", "chainId": 5555, "url": ["https://rpc.chainverse.info"]}, {"name": "QIE Blockchain", "chainId": 5656, "url": ["https://rpc-main1.qiblockchain.online/", "https://rpc-main2.qiblockchain.online/"]}, {"name": "Gana<PERSON>", "chainId": 5777, "url": ["https://127.0.0.1:7545"]}, {"name": "<PERSON><PERSON>", "chainId": 5845, "url": ["https://rpc.tangle.tools"]}, {"name": "Chang Chain Foundation Mainnet", "chainId": 5858, "url": ["https://rpc.cthscan.com/"]}, {"name": "Wegochain Rubidium Mainnet", "chainId": 5869, "url": ["https://proxy.wegochain.io", "http://wallet.wegochain.io:7764"]}, {"name": "BounceBit Mainnet", "chainId": 6001, "url": ["https://fullnode-mainnet.bouncebitapi.com/"]}, {"name": "Tres Mainnet", "chainId": 6066, "url": ["https://rpc.tresleches.finance/", "https://rpc.treschain.io/"]}, {"name": "UPTN", "chainId": 6119, "url": ["https://node-api.uptn.io/v1/ext/rpc"]}, {"name": "Rails", "chainId": 6278, "url": ["https://mainnet.steamexchange.io"]}, {"name": "LAOS", "chainId": 6283, "url": ["https://rpc.laos.laosfoundation.io"]}, {"name": "Aura Mainnet", "chainId": 6322, "url": ["https://jsonrpc.aura.network"]}, {"name": "Digit Soul Smart Chain", "chainId": 6363, "url": ["https://dsc-rpc.digitsoul.co.th"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "chainId": 6502, "url": ["https://peerpay.su.gy/p2p"]}, {"name": "Pixie Chain Mainnet", "chainId": 6626, "url": ["https://http-mainnet.chain.pixie.xyz"]}, {"name": "Cybria Mainnet", "chainId": 6661, "url": ["https://rpc-mainnet.cybria.io"]}, {"name": "Storchain", "chainId": 6667, "url": ["https://rpc.storchain.io"]}, {"name": "Edge Matrix Chain", "chainId": 6678, "url": ["https://rpc1-mainnet.emc.network"]}, {"name": "IRIShub", "chainId": 6688, "url": ["https://evmrpc.irishub-1.irisnet.org", "https://iris-evm.publicnode.com"]}, {"name": "OX Chain", "chainId": 6699, "url": ["https://rpc.oxscan.io"]}, {"name": "PAXB Mainnet", "chainId": 6701, "url": ["https://chain.paxb.io"]}, {"name": "Compverse Mainnet", "chainId": 6779, "url": ["https://rpc.compverse.io/", "https://rpc-useast1.compverse.io/"]}, {"name": "Gold Smart Chain Mainnet", "chainId": 6789, "url": ["https://rpc-mainnet.goldsmartchain.com"]}, {"name": "RACE Mainnet", "chainId": 6805, "url": ["https://racemainnet.io/"]}, {"name": "Pools Mainnet", "chainId": 6868, "url": ["https://rpc.poolsmobility.com"]}, {"name": "MTT Network", "chainId": 6880, "url": ["https://evm-rpc.mtt.network"]}, {"name": "Nibiru cataclysm-1", "chainId": 6900, "url": ["https://evm-rpc.nibiru.fi"]}, {"name": "Laika Mainnet", "chainId": 6942, "url": ["https://mainnetrpc.laikachain.dog"]}, {"name": "Tomb Chain Mainnet", "chainId": 6969, "url": ["https://rpc.tombchain.com/"]}, {"name": "PolySmartChain", "chainId": 6999, "url": ["https://seed0.polysmartchain.com/", "https://seed1.polysmartchain.com/", "https://seed2.polysmartchain.com/"]}, {"name": "ZetaChain Mainnet", "chainId": 7000, "url": ["https://zetachain-evm.blockpi.network/v1/rpc/public", "https://zetachain-mainnet.g.allthatnode.com/archive/evm", "https://zeta-chain.drpc.org", "https://zetachain-mainnet.public.blastapi.io", "https://7000.rpc.thirdweb.com"]}, {"name": "BST Chain", "chainId": 7007, "url": ["https://rpc.bstchain.io/"]}, {"name": "<PERSON> the heart", "chainId": 7027, "url": ["https://rpc.ella.network"]}, {"name": "Planq Mainnet", "chainId": 7070, "url": ["https://evm-rpc.planq.network"]}, {"name": "Bharat Blockchain Network", "chainId": 7099, "url": ["https://bbnrpc.testnet.bharatblockchain.io"]}, {"name": "Nume", "chainId": 7100, "url": ["https://rpc.numecrypto.com"]}, {"name": "0XL3", "chainId": 7117, "url": ["https://rpc.0xl3.com"]}, {"name": "Bitrock Mainnet", "chainId": 7171, "url": ["https://connect.bit-rock.io", "https://brockrpc.io"]}, {"name": "exSat Mainnet", "chainId": 7200, "url": ["https://evm.exsat.network"]}, {"name": "Nexera Mainnet", "chainId": 7208, "url": ["https://rpc.nexera.network"]}, {"name": "InitVerse Mainnet", "chainId": 7233, "url": ["https://rpc-mainnet.inichain.com"]}, {"name": "XPLA Verse", "chainId": 7300, "url": ["https://rpc-xpla-verse.xpla.dev"]}, {"name": "KLYNTAR", "chainId": 7331, "url": ["https://evm.klyntar.org/kly_evm_rpc", "https://evm.klyntarscan.org/kly_evm_rpc"]}, {"name": "Horizen EON Mainnet", "chainId": 7332, "url": ["https://eon-rpc.horizenlabs.io/ethv1", "https://rpc.ankr.com/horizen_eon"]}, {"name": "Shyft Mainnet", "chainId": 7341, "url": ["https://rpc.shyft.network/"]}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 7368, "url": ["https://l2.rarimo.com"]}, {"name": "Raba Network Mainnet", "chainId": 7484, "url": ["https://rpc.x.raba.app/"]}, {"name": "MEVerse Chain Mainnet", "chainId": 7518, "url": ["https://rpc.meversemainnet.io"]}, {"name": "Cyber Mainnet", "chainId": 7560, "url": ["https://cyber.alt.technology/", "https://rpc.cyber.co/"]}, {"name": "Adil Chain V2 Mainnet", "chainId": 7576, "url": ["https://adilchain-rpc.io"]}, {"name": "The Root Network - Mainnet", "chainId": 7668, "url": ["https://root.rootnet.live/archive"]}, {"name": "Canto", "chainId": 7700, "url": ["https://canto.slingshot.finance", "https://canto-rpc.ansybl.io", "https://mainnode.plexnode.org:8545", "https://canto.gravitychain.io/"]}, {"name": "Canto Tesnet", "chainId": 7701, "url": ["https://testnet-archive.plexnode.wtf"]}, {"name": "GDCC MAINNET", "chainId": 7774, "url": ["https://mainnet-rpc-1.gdccscan.io"]}, {"name": "PandaSea Mainnet", "chainId": 7776, "url": ["https://rpc1.pandasea.io"]}, {"name": "Orenium Mainnet Protocol", "chainId": 7778, "url": ["https://validator-mainnet.orenium.org", "https://rpc-oracle-mainnet.orenium.org", "https://portalmainnet.orenium.org"]}, {"name": "Draw Coin", "chainId": 7788, "url": ["https://rpc.drawchain.org"]}, {"name": "MaalChain V2", "chainId": 7862, "url": ["https://node1-mainnet-new.maalscan.io/", "https://node2-mainnet-new.maalscan.io/", "https://node3-mainnet-new.maalscan.io/"]}, {"name": "Powerloom Mainnet", "chainId": 7865, "url": ["https://rpc.powerloom.network"]}, {"name": "Powerloom Mainnet V2", "chainId": 7869, "url": ["https://rpc-v2.powerloom.network"]}, {"name": "<PERSON><PERSON>", "chainId": 7887, "url": ["https://rpc.kinto.xyz/http", "https://kinto-mainnet.calderachain.xyz/http"]}, {"name": "ARDENIUM Athena", "chainId": 7895, "url": ["https://rpc-athena.ardescan.com/"]}, {"name": "arena-z", "chainId": 7897, "url": ["https://rpc.arena-z.gg"]}, {"name": "Dot Blox", "chainId": 7923, "url": ["https://rpc.dotblox.io"]}, {"name": "MO Mainnet", "chainId": 7924, "url": ["https://mainnet-rpc.mochain.app/"]}, {"name": "DOS Chain", "chainId": 7979, "url": ["https://main.doschain.com"]}, {"name": "Teleport", "chainId": 8000, "url": ["https://evm-rpc.teleport.network"]}, {"name": "Polynomial", "chainId": 8008, "url": ["https://rpc.polynomial.fi"]}, {"name": "iSunCoin Mainnet", "chainId": 8017, "url": ["https://mainnet.isuncoin.com"]}, {"name": "BOAT Mainnet", "chainId": 8047, "url": ["https://rpc0.come.boat/"]}, {"name": "Shardeum Liberty 1.X", "chainId": 8080, "url": ["https://liberty10.shardeum.org/"]}, {"name": "Shardeum Liberty 2.X", "chainId": 8081, "url": ["https://liberty20.shardeum.org/"]}, {"name": "Shardeum Sphinx 1.X", "chainId": 8082, "url": ["https://sphinx.shardeum.org/"]}, {"name": "Bitcoin Chain", "chainId": 8086, "url": ["https://rpc.bitcoinevm.org"]}, {"name": "E-Dollar", "chainId": 8087, "url": ["https://rpc.e-dollar.org"]}, {"name": "StreamuX Blockchain", "chainId": 8098, "url": ["https://u0ma6t6heb:<EMAIL>/"]}, {"name": "Bharat Blockchain Network Mainnet", "chainId": 8099, "url": ["https://bbnrpc.mainnet.bharatblockchain.io"]}, {"name": "Shardeum", "chainId": 8118, "url": ["https://api.shardeum.org/"]}, {"name": "<PERSON><PERSON>", "chainId": 8192, "url": ["https://rpc.toruschain.com"]}, {"name": "<PERSON><PERSON>", "chainId": 8217, "url": ["https://public-en.node.kaia.io"]}, {"name": "Space Subnet", "chainId": 8227, "url": ["https://subnets.avax.network/space/mainnet/rpc"]}, {"name": "Blockton Blockchain", "chainId": 8272, "url": ["https://rpc.blocktonscan.com/"]}, {"name": "<PERSON>", "chainId": 8329, "url": ["https://rpc.lorenzo-protocol.xyz"]}, {"name": "B3", "chainId": 8333, "url": ["https://mainnet-rpc.b3.fun"]}, {"name": "Dracones Financial Services", "chainId": 8387, "url": ["https://api.dracones.net/"]}, {"name": "THAT Mainnet", "chainId": 8428, "url": ["https://api.thatchain.io", "https://api.thatchain.io/mainnet"]}, {"name": "Base", "chainId": 8453, "url": ["https://mainnet.base.org/", "https://developer-access-mainnet.base.org/", "https://base.gateway.tenderly.co", "https://base-rpc.publicnode.com"]}, {"name": "Toki Network", "chainId": 8654, "url": ["https://mainnet.buildwithtoki.com/v0/rpc"]}, {"name": "Hela Official Runtime Mainnet", "chainId": 8668, "url": ["https://mainnet-rpc.helachain.com"]}, {"name": "TOOL Global Mainnet", "chainId": 8723, "url": ["https://mainnet-web3.wolot.io"]}, {"name": "Storagechain Mainnet", "chainId": 8726, "url": ["https://mainnet-validator.storagechain.io"]}, {"name": "Bullions Smart Chain", "chainId": 8732, "url": ["https://rpc.bullionsx.org"]}, {"name": "Alph Network", "chainId": 8738, "url": ["https://rpc.alph.network"]}, {"name": "TMY Chain", "chainId": 8768, "url": ["https://node1.tmyblockchain.org/rpc"]}, {"name": "Haven1", "chainId": 8811, "url": ["https://rpc.haven1.org"]}, {"name": "IOTA EVM", "chainId": 8822, "url": ["https://json-rpc.evm.iotaledger.net"]}, {"name": "MARO Blockchain Mainnet", "chainId": 8848, "url": ["https://rpc-mainnet.ma.ro"]}, {"name": "SuperLumio", "chainId": 8866, "url": ["https://mainnet.lumio.io/"]}, {"name": "Lif3 Chain", "chainId": 8869, "url": ["https://rpc.lif3.com"]}, {"name": "Unique", "chainId": 8880, "url": ["https://rpc.unique.network", "https://eu-rpc.unique.network", "https://asia-rpc.unique.network", "https://us-rpc.unique.network"]}, {"name": "Quartz by Unique", "chainId": 8881, "url": ["https://rpc-quartz.unique.network", "https://quartz.api.onfinality.io/public-ws", "https://eu-rpc-quartz.unique.network", "https://asia-rpc-quartz.unique.network", "https://us-rpc-quartz.unique.network"]}, {"name": "Sapphire by <PERSON><PERSON>", "chainId": 8883, "url": ["https://rpc-sapphire.unique.network", "https://us-rpc-sapphire.unique.network", "https://eu-rpc-sapphire.unique.network", "https://asia-rpc-sapphire.unique.network"]}, {"name": "XANAChain", "chainId": 8888, "url": ["https://mainnet.xana.net/rpc"]}, {"name": "Vyvo Smart Chain", "chainId": 8889, "url": ["https://vsc-dataseed.vyvo.org:8889"]}, {"name": "Mammoth Mainnet", "chainId": 8898, "url": ["https://dataseed.mmtscan.io", "https://dataseed1.mmtscan.io", "https://dataseed2.mmtscan.io"]}, {"name": "JIBCHAIN L1", "chainId": 8899, "url": ["https://rpc-l1.jibchain.net", "https://rpc-l1.inan.in.th"]}, {"name": "Algen", "chainId": 8911, "url": ["https://rpc.algen.network"]}, {"name": "Algen Layer2", "chainId": 8921, "url": ["https://rpc.alg2.algen.network"]}, {"name": "Giant Mammoth Mainnet", "chainId": 8989, "url": ["https://rpc-asia.gmmtchain.io"]}, {"name": "b<PERSON><PERSON><PERSON>", "chainId": 8995, "url": ["https://core.bloxberg.org"]}, {"name": "Evmos", "chainId": 9001, "url": ["https://evmos.lava.build", "https://evmos-evm-rpc.publicnode.com"]}, {"name": "Shido Network", "chainId": 9008, "url": ["https://shido-mainnet-archive-lb-nw5es9.zeeve.net/USjg7xqUmCZ4wCsqEOOE/rpc", "https://evm.shidoscan.net"]}, {"name": "BerylBit Mainnet", "chainId": 9012, "url": ["https://mainnet.berylbit.io"]}, {"name": "Nexa Mainnet Block", "chainId": 9025, "url": ["https://rpc-nodes.nexablockscan.io", "https://rpc-nodes-delta.nexablockscan.io"]}, {"name": "Apex Fusion - Nexus Mainnet", "chainId": 9069, "url": ["https://rpc.nexus.mainnet.apexfusion.org/"]}, {"name": "Genesis Coin", "chainId": 9100, "url": ["https://genesis-gn.com"]}, {"name": "Codefin Mainnet", "chainId": 9223, "url": ["https://chain-rpc.codefin.pro"]}, {"name": "Galactica-Reticulum", "chainId": 9302, "url": ["https://evm-rpc-http-reticulum.galactica.com/"]}, {"name": "Z Chain", "chainId": 9369, "url": ["https://rpc.zchain.org"]}, {"name": "Evoke Mainnet", "chainId": 9395, "url": ["https://mainnet-rpc.evokescan.org"]}, {"name": "Load Alphanet", "chainId": 9496, "url": ["https://alphanet.load.network"]}, {"name": "Rebus Mainnet", "chainId": 9696, "url": ["https://apievml2.rebuschain.com/l2rpc"]}, {"name": "Oort MainnetDev", "chainId": 9700, "url": ["https://dev-rpc.oortech.com"]}, {"name": "PepeNetwork Mainnet", "chainId": 9779, "url": ["https://rpc-mainnet.pepenetwork.io"]}, {"name": "Carbon EVM", "chainId": 9790, "url": ["https://evm-api.carbon.network/"]}, {"name": "OptimusZ7 Mainnet", "chainId": 9797, "url": ["https://rpc.optimusz7.com"]}, {"name": "IMPERIUM MAINNET", "chainId": 9819, "url": ["https://data-aws-mainnet.imperiumchain.com", "https://data-aws2-mainnet.imperiumchain.com"]}, {"name": "Dogelayer Mainnet", "chainId": 9888, "url": ["https://dl-rpc.dogelayer.org"]}, {"name": "pointledger", "chainId": 9889, "url": ["https://rpc.pointledger.net"]}, {"name": "Larissa <PERSON>", "chainId": 9898, "url": ["https://rpc.larissa.network"]}, {"name": "Zytron Linea Mainnet", "chainId": 9901, "url": ["https://rpc.zypher.network/"]}, {"name": "Espento Mainnet", "chainId": 9911, "url": ["https://rpc.escscan.com/"]}, {"name": "Combo Mainnet", "chainId": 9980, "url": ["https://rpc.combonetwork.io"]}, {"name": "Volley Mainnet", "chainId": 9981, "url": ["https://main-rpc.volleychain.com"]}, {"name": "MFEV CHAIN MAINNET", "chainId": 9982, "url": ["https://rpc.mfevscan.com"]}, {"name": "Agung Network", "chainId": 9990, "url": ["https://wss-async.agung.peaq.network"]}, {"name": "Mind Smart Chain Mainnet", "chainId": 9996, "url": ["https://rpc-msc.mindchain.info/", "https://seednode.mindchain.info", "https://archive.mindchain.info/", "https://mind-smart-chain.rpc.thirdweb.com"]}, {"name": "Ztc Mainnet", "chainId": 9998, "url": ["https://zitcoin.us"]}, {"name": "Smart Bitcoin Cash", "chainId": 10000, "url": ["https://smartbch.greyh.at", "https://rpc-mainnet.smartbch.org", "https://smartbch.fountainhead.cash/mainnet", "https://smartbch.devops.cash/mainnet"]}, {"name": "DeepSafe Beta Mainnet", "chainId": 10011, "url": ["https://betamainnet-rpc-node-http.deepsafe.network"]}, {"name": "Gon Chain", "chainId": 10024, "url": ["https://node1.testnet.gaiaopen.network", "https://node1.mainnet.gon.network", "https://node2.mainnet.gon.network", "https://node3.mainnet.gon.network", "https://node4.mainnet.gon.network"]}, {"name": "AEON Chain", "chainId": 10025, "url": ["https://node.aeon.xyz/rpc"]}, {"name": "Volcano Chain Mainnet", "chainId": 10085, "url": ["https://mainnet.vchain.pro"]}, {"name": "SJATSH", "chainId": 10086, "url": ["http://geth.free.idcfengye.com"]}, {"name": "<PERSON><PERSON><PERSON><PERSON> Verse", "chainId": 10096, "url": ["https://web3.metanovaverse.com"]}, {"name": "Blockchain Genesis Mainnet", "chainId": 10101, "url": ["https://eu.mainnet.xixoio.com", "https://us.mainnet.xixoio.com", "https://asia.mainnet.xixoio.com"]}, {"name": "MaxxChain Mainnet", "chainId": 10201, "url": ["https://rpc.maxxchain.org", "https://rpc1.maxxchain.org", "https://rpc2.maxxchain.org"]}, {"name": "GLScan", "chainId": 10222, "url": ["https://glc-dataseed.glscan.io/"]}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 10242, "url": ["https://rpc.arthera.net"]}, {"name": "0XTade", "chainId": 10248, "url": ["https://node.0xtchain.com"]}, {"name": "TAO EVM Mainnet", "chainId": 10321, "url": ["https://rpc.taoevm.io"]}, {"name": "Numbers Mainnet", "chainId": 10507, "url": ["https://mainnetrpc.num.network"]}, {"name": "CryptoCoinPay", "chainId": 10823, "url": ["http://node106.cryptocoinpay.info:8545"]}, {"name": "Lamina1", "chainId": 10849, "url": ["https://subnets.avax.network/lamina1/mainnet/rpc"]}, {"name": "Lamina1 Identity", "chainId": 10850, "url": ["https://subnets.avax.network/lamina1id/mainnet/rpc"]}, {"name": "Quadrans Blockchain", "chainId": 10946, "url": ["https://rpc.quadrans.io", "https://rpcna.quadrans.io", "https://rpceu.quadrans.io"]}, {"name": "KB Chain", "chainId": 11000, "url": ["https://mainnet-rpc.kbcfoundation.com"]}, {"name": "Bool Network Beta Mainnet", "chainId": 11100, "url": ["https://beta-rpc-node-http.bool.network"]}, {"name": "Astra", "chainId": 11110, "url": ["https://rpc.astranaut.io", "https://rpc1.astranaut.io"]}, {"name": "WAGMI", "chainId": 11111, "url": ["https://subnets.avax.network/wagmi/wagmi-chain-testnet/rpc"]}, {"name": "HashBit Mainnet", "chainId": 11119, "url": ["https://mainnet-rpc.hashbit.org", "https://rpc.hashbit.org"]}, {"name": "Shine Chain", "chainId": 11221, "url": ["https://rpc.shinescan.io"]}, {"name": "Haqq Network", "chainId": 11235, "url": ["https://rpc.eth.haqq.network", "https://haqq-evm-rpc.publicnode.com", "https://haqq.drpc.org"]}, {"name": "eGold Chain", "chainId": 11451, "url": ["https://rpc.egoldchain.com"]}, {"name": "GEB Mainnet", "chainId": 11501, "url": ["https://rpc-mainnet-1.geb.network/", "https://rpc-mainnet-2.geb.network/"]}, {"name": "GEB Signet", "chainId": 11504, "url": ["https://signet.geb.network/"]}, {"name": "SatsChain", "chainId": 11521, "url": ["https://rpc-satschain-1.bevm.io"]}, {"name": "Artela <PERSON>net", "chainId": 11820, "url": ["https://node-euro.artela.network/rpc"]}, {"name": "Santiment Intelligence Network DEPRECATED", "chainId": 11888, "url": ["https://sanrchain-node.santiment.net"]}, {"name": "Polygon Supernet Arianee", "chainId": 11891, "url": ["https://rpc.polygonsupernet.public.arianee.net"]}, {"name": "SatoshiChain Mainnet", "chainId": 12009, "url": ["https://mainnet-rpc.satoshichain.io"]}, {"name": "Aternos", "chainId": 12020, "url": ["https://rpc.aternoschain.com"]}, {"name": "Singularity ZERO Mainnet", "chainId": 12052, "url": ["https://zerorpc.singularity.gold"]}, {"name": "BRC Chain Mainnet", "chainId": 12123, "url": ["https://rpc.brcchain.io"]}, {"name": "Fibonacci Mainnet", "chainId": 12306, "url": ["https://node1.fibo-api.asia", "https://node2.fibo-api.asia", "https://node3.fibo-api.asia", "https://node4.fibo-api.asia", "https://node5.fibo-api.asia", "https://node6.fibo-api.asia", "https://node7.fibo-api.asia", "https://node1.fibo-rpc.asia", "https://node2.fibo-rpc.asia", "https://node3.fibo-rpc.asia", "https://node4.fibo-rpc.asia", "https://node5.fibo-rpc.asia", "https://node6.fibo-rpc.asia", "https://node7.fibo-rpc.asia"]}, {"name": "Huddle01 dRTC Chain", "chainId": 12323, "url": ["https://huddle01.calderachain.xyz/http"]}, {"name": "L3X Protocol", "chainId": 12324, "url": ["https://rpc-mainnet.l3x.com"]}, {"name": "GDPR Mainnet", "chainId": 12358, "url": ["https://rpc.gdprchain.com"]}, {"name": "RSS3 VSL Mainnet", "chainId": 12553, "url": ["https://rpc.rss3.io"]}, {"name": "SPS", "chainId": 13000, "url": ["https://rpc.ssquad.games"]}, {"name": "Credit Smart Chain", "chainId": 13308, "url": ["https://rpc.creditsmartchain.com"]}, {"name": "Immutable zkEVM", "chainId": 13371, "url": ["https://rpc.immutable.com", "https://immutable-zkevm.drpc.org"]}, {"name": "Phoenix Mainnet", "chainId": 13381, "url": ["https://rpc.phoenixplorer.com/"]}, {"name": "<PERSON><PERSON>", "chainId": 13396, "url": ["https://subnets.avax.network/masanetwork/mainnet/rpc"]}, {"name": "Kronobit Mainnet", "chainId": 13600, "url": ["https://mainnet-rpc.qbitscan.com"]}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 13812, "url": ["https://gateway.opn.network/node/ext/bc/2VsZe5DstWw2bfgdx3YbjKcMsJnNDjni95sZorBEdk9L9Qr9Fr/rpc"]}, {"name": "Bitharvest Chain Mainnet", "chainId": 14149, "url": ["https://rpc.bthscan.io/"]}, {"name": "Bitlazer", "chainId": 14235, "url": ["https://bitlazer.calderachain.xyz/http"]}, {"name": "Poodl Mainnet", "chainId": 15259, "url": ["https://rpc.poodl.org"]}, {"name": "KYMTC Mainnet", "chainId": 15430, "url": ["https://mainnet-rpc.kymaticscan.online"]}, {"name": "LoopNetwork Mainnet", "chainId": 15551, "url": ["https://api.mainnetloop.com"]}, {"name": "MetaDot Mainnet", "chainId": 16000, "url": ["https://mainnet.metadot.network"]}, {"name": "DeFiVerse Mainnet", "chainId": 16116, "url": ["https://rpc.defi-verse.org/"]}, {"name": "Cypherium Mainnet", "chainId": 16166, "url": ["https://pubnodes.cypherium.io/rpc"]}, {"name": "PLYR PHI", "chainId": 16180, "url": ["https://subnets.avax.network/plyr/mainnet/rpc"]}, {"name": "Genesys Mainnet", "chainId": 16507, "url": ["https://rpc.genesys.network"]}, {"name": "AirDAO Mainnet", "chainId": 16718, "url": ["https://network.ambrosus.io"]}, {"name": "<PERSON><PERSON>", "chainId": 17000, "url": ["https://rpc.holesky.ethpandaops.io", "https://ethereum-holesky-rpc.publicnode.com", "https://holesky.drpc.org", "https://rpc-holesky.rockx.com"]}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 17069, "url": ["https://rpc.garnetchain.com"]}, {"name": "Onchain Points", "chainId": 17071, "url": ["https://rpc.onchainpoints.xyz", "https://rpc-onchain-points-8n0qkkpr2j.t.conduit.xyz/{CONDUIT_API_KEY}"]}, {"name": "G8Chain Mainnet", "chainId": 17171, "url": ["https://mainnet-rpc.oneg8.network"]}, {"name": "Eclipse Subnet", "chainId": 17172, "url": ["https://subnets.avax.network/eclipse/testnet/rpc"]}, {"name": "KONET Mainnet", "chainId": 17217, "url": ["https://api.kon-wallet.com"]}, {"name": "Esports Chain", "chainId": 17735, "url": ["https://esportsblock.org/rpc/"]}, {"name": "EOS EVM Network", "chainId": 17777, "url": ["https://api.evm.eosnetwork.com"]}, {"name": "Smart Trade Networks", "chainId": 18122, "url": ["https://beefledgerwallet.com:8544"]}, {"name": "Proof Of Memes", "chainId": 18159, "url": ["https://mainnet-rpc.memescan.io", "https://mainnet-rpc2.memescan.io", "https://mainnet-rpc3.memescan.io", "https://mainnet-rpc4.memescan.io"]}, {"name": "unreal-old", "chainId": 18231, "url": ["https://rpc.unreal.gelato.digital"]}, {"name": "unreal", "chainId": 18233, "url": ["https://rpc.unreal-orbit.gelato.digital"]}, {"name": "MXC zkEVM Moonchain", "chainId": 18686, "url": ["https://rpc.mxc.com"]}, {"name": "Titan (TKX)", "chainId": 18888, "url": ["https://titan-json-rpc.titanlab.io", "https://titan-json-rpc-tokyo.titanlab.io", "https://titan-json-rpc-seoul.titanlab.io", "https://titan-json-rpc-hongkong.titanlab.io"]}, {"name": "HOME Verse Mainnet", "chainId": 19011, "url": ["https://rpc.mainnet.oasys.homeverse.games/"]}, {"name": "LocaChain Mainnet", "chainId": 19180, "url": ["https://tgrpntwm.locachain.io"]}, {"name": "BlockX Mainnet", "chainId": 19191, "url": ["https://web3.blockxnet.com"]}, {"name": "Decentraconnect Social", "chainId": 19224, "url": ["https://rpc.decentraconnect.io"]}, {"name": "SEC Mainnet", "chainId": 19516, "url": ["https://rpc.secexplorer.io"]}, {"name": "Magnet Network", "chainId": 19527, "url": ["https://magnet-rpc.magport.io/"]}, {"name": "LBRY Mainnet", "chainId": 19600, "url": ["https://lbry.nl/rpc"]}, {"name": "BTCIX Network", "chainId": 19845, "url": ["https://seed.btcix.org/rpc"]}, {"name": "Ultra EVM Network", "chainId": 19991, "url": ["https://evm.ultra.eosusa.io"]}, {"name": "Camelark Mainnet", "chainId": 20001, "url": ["https://mainnet-http-rpc.camelark.com"]}, {"name": "Niza Chain Mainnet", "chainId": 20041, "url": ["https://nizascan.io/rpc"]}, {"name": "XUSD ONE StableChain Mainnet", "chainId": 20441, "url": ["https://xusd.live"]}, {"name": "P12 Chain", "chainId": 20736, "url": ["https://rpc-chain.p12.games"]}, {"name": "Jono11 Subnet", "chainId": 20765, "url": ["https://subnets.avax.network/jono11/testnet/rpc"]}, {"name": "Action Mainnet", "chainId": 21000, "url": ["https://rpc.actionblockchain.org"]}, {"name": "C4EI", "chainId": 21004, "url": ["https://rpc.c4ei.net"]}, {"name": "All About Healthy", "chainId": 21133, "url": ["https://rpc.c4ex.net"]}, {"name": "1Money Network Mainnet", "chainId": 21210, "url": ["https://mainnet.1money.network"]}, {"name": "DCpay Mainnet", "chainId": 21223, "url": ["https://rpc.dcpay.io"]}, {"name": "CENNZnet Azalea", "chainId": 21337, "url": ["https://cennznet.unfrastructure.io/public"]}, {"name": "Lestnet", "chainId": 21363, "url": ["https://service.lestnet.org"]}, {"name": "omChain <PERSON>", "chainId": 21816, "url": ["https://seed.omchain.io"]}, {"name": "BSL Mainnet", "chainId": 21912, "url": ["http://rpc-mainnet.nftruth.io:8545"]}, {"name": "Taycan", "chainId": 22023, "url": ["https://taycan-rpc.hupayx.io:8545"]}, {"name": "Nautilus Mainnet", "chainId": 22222, "url": ["https://api.nautilus.nautchain.xyz"]}, {"name": "MAP Protocol", "chainId": 22776, "url": ["https://rpc.maplabs.io"]}, {"name": "PremiumBlock", "chainId": 23023, "url": ["https://rpc.premiumblock.org"]}, {"name": "Oasis Sapphire", "chainId": 23294, "url": ["https://sapphire.oasis.io"]}, {"name": "DreyerX Mainnet", "chainId": 23451, "url": ["https://rpc.dreyerx.com"]}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 24116, "url": ["https://testnet.steamexchange.io"]}, {"name": "Nexurachain", "chainId": 24125, "url": ["https://rpc.nexurachain.io"]}, {"name": "MintMe.com Coin", "chainId": 24734, "url": ["https://node1.mintme.com"]}, {"name": "Recall", "chainId": 24816, "url": ["https://evm.node-0.mainnet.recall.network"]}, {"name": "LiquidLayer Mainnet", "chainId": 25186, "url": ["https://mainnet.liquidlayer.network"]}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 25327, "url": ["https://rpc.everclear.raas.gelato.cloud"]}, {"name": "Hammer Chain Mainnet", "chainId": 25888, "url": ["https://www.hammerchain.io/rpc"]}, {"name": "Ferrum Quantum Portal Network", "chainId": 26100, "url": ["https://qpn.svcs.ferrumnetwork.io"]}, {"name": "DucatusX", "chainId": 26483, "url": ["https://ducx-mainnet-node1.rocknblock.io", "https://ducx-mainnet-node2.rocknblock.io", "https://ducx-mainnet-node3.rocknblock.io"]}, {"name": "Hertz Network Mainnet", "chainId": 26600, "url": ["https://mainnet-rpc.hertzscan.com"]}, {"name": "OasisChain Mainnet", "chainId": 26863, "url": ["https://rpc1.oasischain.io", "https://rpc2.oasischain.io", "https://rpc3.oasischain.io"]}, {"name": "KLAOS Nova", "chainId": 27181, "url": ["https://rpc.klaosnova.laosfoundation.io"]}, {"name": "zeroone Mainnet Subnet", "chainId": 27827, "url": ["https://subnets.avax.network/zeroonemai/mainnet/rpc"]}, {"name": "XferChain Mainnet", "chainId": 28125, "url": ["https://rpc.xferchain.org"]}, {"name": "Vizing Mainnet", "chainId": 28518, "url": ["https://rpc.vizing.com"]}, {"name": "Nexa MetaNet", "chainId": 29223, "url": ["https://nexa.sh/metanet"]}, {"name": "MCH Verse Mainnet", "chainId": 29548, "url": ["https://rpc.oasys.mycryptoheroes.net"]}, {"name": "q<PERSON>hain <PERSON>", "chainId": 30000, "url": ["https://rpc.qchain.kr"]}, {"name": "MiYou <PERSON>", "chainId": 30088, "url": ["https://blockchain.miyou.io", "https://blockchain.miyoulab.com"]}, {"name": "Ethersocial Network", "chainId": 31102, "url": ["https://api.esn.gonspool.com"]}, {"name": "CloudTx Mainnet", "chainId": 31223, "url": ["https://mainnet-rpc.cloudtx.finance"]}, {"name": "Wirex Pay Mainnet", "chainId": 31415, "url": ["https://rpc.wirexpaychain.com"]}, {"name": "<PERSON><PERSON>", "chainId": 31612, "url": ["https://rpc_evm-mezo.imperator.co", "https://jsonrpc-mezo.boar.network", "https://mainnet.mezo.public.validationcloud.io", "https://rpc-internal.mezo.org"]}, {"name": "BasedAI", "chainId": 32323, "url": ["https://mainnet.basedaibridge.com/rpc/"]}, {"name": "Santiment Intelligence Network", "chainId": 32382, "url": ["https://node.sanr.app"]}, {"name": "Bitgert Mainnet", "chainId": 32520, "url": ["https://rpc-bitgert.icecreamswap.com", "https://mainnet-rpc.brisescan.com", "https://chainrpc.com", "https://serverrpc.com"]}, {"name": "Fusion Mainnet", "chainId": 32659, "url": ["https://mainnet.fusionnetwork.io"]}, {"name": "Zilliqa EVM", "chainId": 32769, "url": ["https://api.zilliqa.com"]}, {"name": "Zilliqa 2 EVM proto-mainnet", "chainId": 32770, "url": ["https://api.zq2-protomainnet.zilliqa.com"]}, {"name": "Zilliqa EVM Isolated Server", "chainId": 32990, "url": ["https://zilliqa-isolated-server.zilliqa.com/"]}, {"name": "Entangle Mainnet", "chainId": 33033, "url": ["https://json-rpc.entangle.fi"]}, {"name": "<PERSON>", "chainId": 33111, "url": ["https://rpc.curtis.apechain.com"]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chainId": 33139, "url": ["https://rpc.apechain.com"]}, {"name": "Cloudverse Subnet", "chainId": 33210, "url": ["https://subnets.avax.network/cloudverse/mainnet/rpc"]}, {"name": "Aves Mainnet", "chainId": 33333, "url": ["https://rpc.avescoin.io"]}, {"name": "SlingShot", "chainId": 33401, "url": ["https://rpc.slingshotdao.com"]}, {"name": "<PERSON><PERSON>", "chainId": 33979, "url": ["https://rpc-mainnet.funkichain.com"]}, {"name": "Mode", "chainId": 34443, "url": ["https://mainnet.mode.network", "https://mode.drpc.org"]}, {"name": "ZEUS Mainnet", "chainId": 34504, "url": ["https://mainnet-rpc.zeuschainscan.io"]}, {"name": "J2O Taro", "chainId": 35011, "url": ["https://rpc.j2o.io"]}, {"name": "Q Mainnet", "chainId": 35441, "url": ["https://rpc.q.org"]}, {"name": "ConnectorManager", "chainId": 38400, "url": ["https://cm.rangersprotocol.com/api/jsonrpc"]}, {"name": "ConnectorManager Robin", "chainId": 38401, "url": ["https://robin-cm.rangersprotocol.com/api/jsonrpc"]}, {"name": "PRM Mainnet", "chainId": 39656, "url": ["https://mainnet-rpc.prmscan.org"]}, {"name": "Energi Mainnet", "chainId": 39797, "url": ["https://nodeapi.energi.network"]}, {"name": "OHO Mainnet", "chainId": 39815, "url": ["https://mainnet.oho.ai"]}, {"name": "DIV Chain", "chainId": 40000, "url": ["https://rpc.divchain.org"]}, {"name": "Aleph Zero EVM", "chainId": 41455, "url": ["https://rpc.alephzero.raas.gelato.cloud"]}, {"name": "Opulent-X BETA", "chainId": 41500, "url": ["https://connect.opulent-x.com"]}, {"name": "EDU Chain", "chainId": 41923, "url": ["https://rpc.edu-chain.raas.gelato.cloud"]}, {"name": "PMON Chain", "chainId": 42001, "url": ["https://rpc.pmon.xyz"]}, {"name": "Donatuz", "chainId": 42026, "url": ["https://rpc.donatuz.com"]}, {"name": "Arbitrum One", "chainId": 42161, "url": ["https://arb1.arbitrum.io/rpc", "https://arbitrum-mainnet.infura.io/v3/", "https://rpc.arb1.arbitrum.gateway.fm"]}, {"name": "Arbitrum Nova", "chainId": 42170, "url": ["https://nova.arbitrum.io/rpc", "https://arbitrum-nova-rpc.publicnode.com"]}, {"name": "Celo <PERSON>net", "chainId": 42220, "url": ["https://forno.celo.org"]}, {"name": "Oasis Emerald", "chainId": 42262, "url": ["https://emerald.oasis.io"]}, {"name": "GoldXChain Mainnet", "chainId": 42355, "url": ["https://mainnet-rpc.goldxchain.io"]}, {"name": "Asset Chain Mainnet", "chainId": 42420, "url": ["https://mainnet-rpc.assetchain.org"]}, {"name": "ZKFair Mainnet", "chainId": 42766, "url": ["https://rpc.zkfair.io"]}, {"name": "Etherlink Mainnet", "chainId": 42793, "url": ["https://node.mainnet.etherlink.com"]}, {"name": "Athereum", "chainId": 43110, "url": ["https://ava.network:21015/ext/evm/rpc"]}, {"name": "<PERSON><PERSON>", "chainId": 43111, "url": ["https://rpc.hemi.network/rpc"]}, {"name": "Avalanche C-Chain", "chainId": 43114, "url": ["https://api.avax.network/ext/bc/C/rpc", "https://rpc.ankr.com/avalanche", "https://ava-mainnet.public.blastapi.io/ext/bc/C/rpc"]}, {"name": "<PERSON><PERSON>", "chainId": 43288, "url": ["https://avax.boba.network", "https://replica.avax.boba.network"]}, {"name": "GUNZ", "chainId": 43419, "url": ["https://rpc.gunzchain.io/ext/bc/2M47TxWHGnhNtq6pM5zPXdATBtuqubxn5EPFgFmEawCQr9WFML/rpc"]}, {"name": "Formicarium", "chainId": 43521, "url": ["https://rpc.formicarium.memecore.net"]}, {"name": "<PERSON><PERSON>", "chainId": 44444, "url": ["https://rpc-02.frenscan.io"]}, {"name": "Quantum Network", "chainId": 44445, "url": ["https://rpcqtm.avescoin.io"]}, {"name": "Autobahn Network", "chainId": 45000, "url": ["https://rpc.autobahn.network"]}, {"name": "Juneo JUNE-Chain", "chainId": 45003, "url": ["https://rpc.juneo-mainnet.network/ext/bc/JUNE/rpc"]}, {"name": "Juneo DAI1-Chain", "chainId": 45004, "url": ["https://rpc.juneo-mainnet.network/ext/bc/DAI1/rpc"]}, {"name": "Juneo USDT1-Chain", "chainId": 45005, "url": ["https://rpc.juneo-mainnet.network/ext/bc/USDT1/rpc"]}, {"name": "Juneo USD1-Chain", "chainId": 45006, "url": ["https://rpc.juneo-mainnet.network/ext/bc/USD1/rpc"]}, {"name": "Juneo mBTC1-Chain", "chainId": 45007, "url": ["https://rpc.juneo-mainnet.network/ext/bc/mBTC1/rpc"]}, {"name": "Juneo GLD1-Chain", "chainId": 45008, "url": ["https://rpc.juneo-mainnet.network/ext/bc/GLD1/rpc"]}, {"name": "Juneo LTC1-Chain", "chainId": 45009, "url": ["https://rpc.juneo-mainnet.network/ext/bc/LTC1/rpc"]}, {"name": "Juneo DOGE1-Chain", "chainId": 45010, "url": ["https://rpc.juneo-mainnet.network/ext/bc/DOGE1/rpc"]}, {"name": "Juneo EUR1-Chain", "chainId": 45011, "url": ["https://rpc.juneo-mainnet.network/ext/bc/EUR1/rpc"]}, {"name": "Juneo SGD1-Chain", "chainId": 45012, "url": ["https://rpc.juneo-mainnet.network/ext/bc/SGD1/rpc"]}, {"name": "Juneo BCH1-Chain", "chainId": 45013, "url": ["https://rpc.juneo-mainnet.network/ext/bc/BCH1/rpc"]}, {"name": "Juneo LINK1-Chain", "chainId": 45014, "url": ["https://rpc.juneo-mainnet.network/ext/bc/LINK1/rpc"]}, {"name": "Swamps L2", "chainId": 45454, "url": ["https://swamps.tc.l2aas.com"]}, {"name": "Deelance Mainnet", "chainId": 45510, "url": ["https://rpc.deelance.com"]}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 45513, "url": ["https://blessnet.calderachain.xyz/http"]}, {"name": "Neo X Mainnet", "chainId": 47763, "url": ["https://mainnet-1.rpc.banelabs.org", "https://mainnet-2.rpc.banelabs.org"]}, {"name": "ReDeFi Layer 1", "chainId": 47803, "url": ["https://layer1.redefi.world"]}, {"name": "REI Network", "chainId": 47805, "url": ["https://rpc.rei.network"]}, {"name": "Zircuit Mainnet", "chainId": 48900, "url": ["https://mainnet.zircuit.com"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "chainId": 50000, "url": ["https://rpc.citronus.com"]}, {"name": "Liveplex OracleEVM", "chainId": 50001, "url": ["https://rpc.oracle.liveplex.io"]}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 50005, "url": ["https://rpc.yooldo-verse.xyz/"]}, {"name": "<PERSON><PERSON>", "chainId": 50104, "url": ["https://rpc.sophon.xyz"]}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 50888, "url": ["https://api.erbie.io"]}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 51712, "url": ["https://mainnet-rpc.sardisnetwork.com"]}, {"name": "Electroneum Mainnet", "chainId": 52014, "url": ["https://rpc.electroneum.com", "https://rpc.ankr.com/electroneum"]}, {"name": "DOID", "chainId": 53277, "url": ["https://rpc.doid.tech"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "chainId": 53456, "url": ["https://rpc.birdlayer.xyz", "https://rpc1.birdlayer.xyz"]}, {"name": "DFK Chain", "chainId": 53935, "url": ["https://subnets.avax.network/defi-kingdoms/dfk-chain/rpc"]}, {"name": "OverProtocol Mainnet", "chainId": 54176, "url": ["https://rpc.overprotocol.com"]}, {"name": "Titan", "chainId": 55004, "url": ["https://rpc.titan.tokamak.network"]}, {"name": "Superposition", "chainId": 55244, "url": ["https://rpc.superposition.so"]}, {"name": "REI Chain Mainnet", "chainId": 55555, "url": ["https://rei-rpc.moonrhythm.io"]}, {"name": "Flamma Mainnet", "chainId": 55614, "url": ["https://rpc.flamma.network"]}, {"name": "Lambda Chain Mainnet", "chainId": 56026, "url": ["https://nrpc.lambda.im/"]}, {"name": "Boba BNB Mainnet", "chainId": 56288, "url": ["https://bnb.boba.network", "https://boba-bnb.gateway.tenderly.co/", "https://gateway.tenderly.co/public/boba-bnb", "https://replica.bnb.boba.network"]}, {"name": "VELO Labs Mainnet", "chainId": 56789, "url": ["https://nova.velo.org"]}, {"name": "Ink", "chainId": 57073, "url": ["https://rpc-gel.inkonchain.com", "https://rpc-qnd.inkonchain.com"]}, {"name": "COINSEC Network", "chainId": 57451, "url": ["https://mainnet-rpc.coinsec.network"]}, {"name": "Linea", "chainId": 59144, "url": ["https://rpc.linea.build", "https://linea-rpc.publicnode.com"]}, {"name": "Genesys Code Mainnet", "chainId": 59971, "url": ["https://mainnet.genesyscode.io/"]}, {"name": "BOB", "chainId": 60808, "url": ["https://rpc.gobob.xyz", "https://bob-mainnet.public.blastapi.io"]}, {"name": "Orange Chain Mainnet", "chainId": 61022, "url": ["https://rpc.orangechain.xyz", "https://hk-rpc.orangechain.xyz"]}, {"name": "Treasure", "chainId": 61166, "url": ["https://rpc.treasure.lol"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "chainId": 61406, "url": ["https://mainnet-rpc.kaichain.net"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "chainId": 61800, "url": ["https://aium-rpc-dev.viacube.com"]}, {"name": "Etica Mainnet", "chainId": 61803, "url": ["https://eticamainnet.eticascan.org", "https://eticamainnet.eticaprotocol.org"]}, {"name": "DoKEN Super Chain Mainnet", "chainId": 61916, "url": ["https://sgrpc.doken.dev", "https://nyrpc.doken.dev", "https://ukrpc.doken.dev"]}, {"name": "Optopia Mainnet", "chainId": 62050, "url": ["https://rpc-mainnet.optopia.ai", "https://rpc-mainnet-2.optopia.ai"]}, {"name": "MultiVAC Mainnet", "chainId": 62621, "url": ["https://rpc.mtv.ac", "https://rpc-eu.mtv.ac"]}, {"name": "eSync Network Mainnet", "chainId": 63000, "url": ["https://rpc.esync.network", "https://rpc.ecredits.com"]}, {"name": "<PERSON><PERSON>st <PERSON>net", "chainId": 63157, "url": ["https://geist-mainnet.g.alchemy.com/public"]}, {"name": "Vecno Mainnet", "chainId": 65357, "url": ["https://rpc.vecno.org"]}, {"name": "Scolcoin Mainnet", "chainId": 65450, "url": ["https://mainnet-rpc.scolcoin.com"]}, {"name": "CyberChain Mainnet", "chainId": 65535, "url": ["https://rpc.cyberchain.xyz/"]}, {"name": "Automata Mainnet", "chainId": 65536, "url": ["https://rpc.ata.network", "https://automata-mainnet.alt.technology/"]}, {"name": "SiriusNet", "chainId": 67390, "url": ["https://u0tnafcv6j:<EMAIL>/"]}, {"name": "Cosmic Chain", "chainId": 67588, "url": ["http://testnet.cosmicchain.site:3344"]}, {"name": "DM2 Verse Mainnet", "chainId": 68770, "url": ["https://rpc.dm2verse.dmm.com"]}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 69420, "url": ["https://rpc.condrieu.ethdevops.io:8545"]}, {"name": "Thinkium Mainnet Chain 0", "chainId": 70000, "url": ["https://proxy.thinkiumrpc.net/"]}, {"name": "Thinkium Mainnet Chain 1", "chainId": 70001, "url": ["https://proxy1.thinkiumrpc.net/"]}, {"name": "Thinkium Mainnet Chain 2", "chainId": 70002, "url": ["https://proxy2.thinkiumrpc.net/"]}, {"name": "Thinkium Mainnet Chain 103", "chainId": 70103, "url": ["https://proxy103.thinkiumrpc.net/"]}, {"name": "Proof of Play - Apex", "chainId": 70700, "url": ["https://rpc.apex.proofofplay.com"]}, {"name": "Proof of Play - Boss", "chainId": 70701, "url": ["https://rpc.boss.proofofplay.com"]}, {"name": "GuapcoinX", "chainId": 71111, "url": ["https://rpc-mainnet.guapcoinx.com/", "https://rpc-mainnet-1.guapcoinx.com/", "https://rpc-mainnet-2.guapcoinx.com/"]}, {"name": "Godwoken Mainnet", "chainId": 71402, "url": ["https://v1.mainnet.godwoken.io/rpc"]}, {"name": "CAGA mainnet", "chainId": 72888, "url": ["https://cagamainnet.com"]}, {"name": "Grok Chain Mainnet", "chainId": 72992, "url": ["https://mainnet-rpc.grokchain.dev"]}, {"name": "ICB Network", "chainId": 73115, "url": ["https://rpc1-mainnet.icbnetwork.info/", "https://rpc2-mainnet.icbnetwork.info/"]}, {"name": "Mixin Virtual Machine", "chainId": 73927, "url": ["https://geth.mvm.dev"]}, {"name": "GEEK Verse Mainnet", "chainId": 75512, "url": ["https://rpc.geekout-pte.com"]}, {"name": "BORAchain mainnet", "chainId": 77001, "url": ["https://public-node.api.boraportal.com/bora/mainnet", "https://public-node.api.boraportal.io/bora/mainnet"]}, {"name": "Vention Smart Chain Mainnet", "chainId": 77612, "url": ["https://mainnet-rpc.vention.network"]}, {"name": "Cycle Network Mainnet Sailboat", "chainId": 77677, "url": ["https://sailboat-rpc-mainnet.cyclenetwork.io"]}, {"name": "Toronet Mainnet", "chainId": 77777, "url": ["https://www.toronet.org/rpc2/"]}, {"name": "Dragonfly Mainnet (Hexapod)", "chainId": 78281, "url": ["https://dragonfly-rpc.switch.ch", "https://dragonfly-rpc.kore-technologies.ch", "https://dragonfly-rpc.phoenix-systems.io", "https://dragonfly-rpc.block-spirit.ch"]}, {"name": "Amplify Subnet", "chainId": 78430, "url": ["https://subnets.avax.network/amplify/testnet/rpc"]}, {"name": "Bulletin Subnet", "chainId": 78431, "url": ["https://subnets.avax.network/bulletin/testnet/rpc"]}, {"name": "Conduit Subnet", "chainId": 78432, "url": ["https://subnets.avax.network/conduit/testnet/rpc"]}, {"name": "Vanguard", "chainId": 78600, "url": ["https://rpc-vanguard.vanarchain.com"]}, {"name": "<PERSON><PERSON>", "chainId": 80002, "url": ["https://rpc-amoy.polygon.technology", "https://polygon-amoy-bor-rpc.publicnode.com"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "chainId": 80069, "url": ["https://bepolia.rpc.berachain.com"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "chainId": 80094, "url": ["https://rpc.berachain.com", "https://berachain-rpc.publicnode.com", "https://rpc.berachain-apis.com"]}, {"name": "Hizoco mainnet", "chainId": 80096, "url": ["https://hizoco.net/rpc"]}, {"name": "Geo Genesis", "chainId": 80451, "url": ["https://rpc-geo-genesis-h0q2s21xx8.t.conduit.xyz/"]}, {"name": "Forta Chain", "chainId": 80931, "url": ["https://rpc-forta-chain-8gj1qndmfc.t.conduit.xyz"]}, {"name": "Nordek Mainnet", "chainId": 81041, "url": ["https://mainnet-rpc.nordekscan.com"]}, {"name": "Blast", "chainId": 81457, "url": ["https://rpc.blast.io", "https://rpc.ankr.com/blast", "https://blast.din.dev/rpc", "https://blastl2-mainnet.public.blastapi.io", "https://blast.blockpi.network/v1/rpc/public", "https://blast-rpc.publicnode.com"]}, {"name": "Quantum Chain Mainnet", "chainId": 81720, "url": ["https://rpc.quantumscan.org"]}, {"name": "VEMP Horizon", "chainId": 82614, "url": ["https://vemp-horizon.calderachain.xyz/http"]}, {"name": "Esa", "chainId": 83278, "url": ["http://65.108.151.70:8545"]}, {"name": "ZEDXION", "chainId": 83872, "url": ["https://mainnet-rpc.zedscan.net"]}, {"name": "O Chain", "chainId": 84841, "url": ["https://rpc.o.xyz", "https://84841.rpc.thirdweb.com"]}, {"name": "Aerie Network", "chainId": 84886, "url": ["https://mainnet.aerielab.io"]}, {"name": "CYBERTRUST", "chainId": 85449, "url": ["http://testnet.cybertrust.space:48501"]}, {"name": "InoAi", "chainId": 88559, "url": ["https://inoai-network.com"]}, {"name": "ZKasino Mainnet", "chainId": 88800, "url": ["https://rpc.zkas.zeeve.net"]}, {"name": "Unit Zero Mainnet", "chainId": 88811, "url": ["https://rpc.unit0.dev"]}, {"name": "Unit Zero Stagenet", "chainId": 88819, "url": ["https://rpc-stagenet.unit0.dev"]}, {"name": "Chiliz Chain Mainnet", "chainId": 88888, "url": ["https://rpc.chiliz.com", "https://rpc.ankr.com/chiliz", "https://chiliz.publicnode.com"]}, {"name": "Unite", "chainId": 88899, "url": ["https://unite-mainnet.g.alchemy.com/public"]}, {"name": "UBIT SMARTCHAIN MAINNET", "chainId": 90002, "url": ["https://rpc.ubitscan.io"]}, {"name": "Beverly Hills", "chainId": 90210, "url": ["https://rpc.beverlyhills.ethdevops.io:8545"]}, {"name": "Nautilus Trition Chain", "chainId": 91002, "url": ["https://triton.api.nautchain.xyz"]}, {"name": "Henez Chain Mainnet", "chainId": 91111, "url": ["https://henez.calderachain.xyz/http"]}, {"name": "MetaDAP Enterprise Mainnet", "chainId": 91120, "url": ["https://rpc.chain.metadap.io"]}, {"name": "Miracle Chain", "chainId": 92278, "url": ["https://rpc.miracleplay.io"]}, {"name": "XCHAIN", "chainId": 94524, "url": ["https://xchain-rpc.kuma.bid"]}, {"name": "SRICHAIN", "chainId": 95432, "url": ["https://rpc.sriscan.com/"]}, {"name": "Lux Mainnet", "chainId": 96369, "url": ["https://api.lux.network"]}, {"name": "Lumoz Chain Mainnet", "chainId": 96370, "url": ["https://rpc.lumoz.org", "https://rpc-hk.lumoz.org"]}, {"name": "Tetron Smart Chain", "chainId": 97055, "url": ["https://rpc.tscscan.org"]}, {"name": "Sidra Chain", "chainId": 97453, "url": ["https://node.sidrachain.com"]}, {"name": "MetaBenz CHAIN", "chainId": 97766, "url": ["https://rpc.metabenzscan.com"]}, {"name": "Plume (Legacy)", "chainId": 98865, "url": ["https://rpc.plumenetwork.xyz"]}, {"name": "Plume Mainnet", "chainId": 98866, "url": ["https://phoenix-rpc.plumenetwork.xyz"]}, {"name": "Ebi Chain", "chainId": 98881, "url": ["https://rpc.ebi.xyz"]}, {"name": "UB Smart Chain", "chainId": 99999, "url": ["https://rpc.uschain.network"]}, {"name": "QuarkChain Mainnet Root", "chainId": 100000, "url": ["http://jrpc.mainnet.quarkchain.io:38391"]}, {"name": "QuarkChain Mainnet Shard 0", "chainId": 100001, "url": ["https://mainnet-s0-ethapi.quarkchain.io", "http://eth-jrpc.mainnet.quarkchain.io:39000"]}, {"name": "QuarkChain Mainnet Shard 1", "chainId": 100002, "url": ["https://mainnet-s1-ethapi.quarkchain.io", "http://eth-jrpc.mainnet.quarkchain.io:39001"]}, {"name": "QuarkChain Mainnet Shard 2", "chainId": 100003, "url": ["https://mainnet-s2-ethapi.quarkchain.io", "http://eth-jrpc.mainnet.quarkchain.io:39002"]}, {"name": "QuarkChain Mainnet Shard 3", "chainId": 100004, "url": ["https://mainnet-s3-ethapi.quarkchain.io", "http://eth-jrpc.mainnet.quarkchain.io:39003"]}, {"name": "QuarkChain Mainnet Shard 4", "chainId": 100005, "url": ["https://mainnet-s4-ethapi.quarkchain.io", "http://eth-jrpc.mainnet.quarkchain.io:39004"]}, {"name": "QuarkChain Mainnet Shard 5", "chainId": 100006, "url": ["https://mainnet-s5-ethapi.quarkchain.io", "http://eth-jrpc.mainnet.quarkchain.io:39005"]}, {"name": "QuarkChain Mainnet Shard 6", "chainId": 100007, "url": ["https://mainnet-s6-ethapi.quarkchain.io", "http://eth-jrpc.mainnet.quarkchain.io:39006"]}, {"name": "QuarkChain Mainnet Shard 7", "chainId": 100008, "url": ["https://mainnet-s7-ethapi.quarkchain.io", "http://eth-jrpc.mainnet.quarkchain.io:39007"]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chainId": 100009, "url": ["https://rpc-mainnet.vechain.energy"]}, {"name": "QuarkChain L2 Mainnet", "chainId": 100011, "url": ["https://mainnet-l2-ethapi.quarkchain.io"]}, {"name": "Socotra JUNE-Chain", "chainId": 101003, "url": ["https://rpc.socotra-testnet.network/ext/bc/JUNE/rpc"]}, {"name": "Global Trust Network", "chainId": 101010, "url": ["https://gtn.stabilityprotocol.com"]}, {"name": "Xitcoin", "chainId": 101088, "url": ["https://network.xitcoin.org"]}, {"name": "Creditcoin", "chainId": 102030, "url": ["https://mainnet3.creditcoin.network"]}, {"name": "Crystaleum", "chainId": 103090, "url": ["https://evm.cryptocurrencydevs.org", "https://rpc.crystaleum.org"]}, {"name": "KaspaClassic Mainnet", "chainId": 104566, "url": ["https://api.kaspaclassic.world/", "http://80.178.101.118:8000/"]}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 105105, "url": ["https://rpc.stratisevm.com"]}, {"name": "BROChain Mainnet", "chainId": 108801, "url": ["https://rpc.brochain.org", "http://rpc.brochain.org", "https://rpc.brochain.org/mainnet", "http://rpc.brochain.org/mainnet"]}, {"name": "Mars Credit", "chainId": 110110, "url": ["https://node99-production-dd5f.up.railway.app:443", "https://rpc.marscredit.xyz:443"]}, {"name": "Siberium Network", "chainId": 111111, "url": ["https://rpc.main.siberium.net", "https://rpc.main.siberium.net.ru"]}, {"name": "re.al", "chainId": 111188, "url": ["https://rpc.realforreal.gelato.digital", "https://tangible-real.gateway.tenderly.co", "https://real.drpc.org"]}, {"name": "Metachain One Mainnet", "chainId": 112358, "url": ["https://rpc.metachain.one", "https://rpc2.metachain.one"]}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 121224, "url": ["https://rpc.fushuma.com"]}, {"name": "Gemchain", "chainId": 123321, "url": ["https://evm-rpc.gemchain.org"]}, {"name": "ETND Chain Mainnets", "chainId": 131419, "url": ["https://rpc.node1.etnd.pro/"]}, {"name": "ICPlaza Mainnet", "chainId": 142857, "url": ["https://rpcmainnet.ic-plaza.org/"]}, {"name": "Odyssey Chain Mainnet", "chainId": 153153, "url": ["https://node.dioneprotocol.com/ext/bc/D/rpc"]}, {"name": "CryptoX", "chainId": 158245, "url": ["https://rpc-xcoin.cryptoxnetwork.io"]}, {"name": "XCOIN", "chainId": 158345, "url": ["https://rpc-xcoin.cryptoxnetwork.io"]}, {"name": "Eventum Mainnet", "chainId": 161803, "url": ["https://mainnet-rpc.evedex.com"]}, {"name": "Eclat <PERSON>net", "chainId": 165279, "url": ["https://mainnet-rpc.eclatscan.com"]}, {"name": "Taiko <PERSON>ethia", "chainId": 167000, "url": ["https://rpc.mainnet.taiko.xyz", "https://taiko-rpc.publicnode.com"]}, {"name": "Taiko Grimsvotn L2", "chainId": 167005, "url": ["https://rpc.test.taiko.xyz"]}, {"name": "Taiko Eldfell L3", "chainId": 167006, "url": ["https://rpc.l3test.taiko.xyz"]}, {"name": "Taiko Jolnir L2", "chainId": 167007, "url": ["https://rpc.jolnir.taiko.xyz"]}, {"name": "<PERSON><PERSON>", "chainId": 167009, "url": ["https://rpc.hekla.taiko.xyz", "https://taiko-hekla-rpc.publicnode.com"]}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 168168, "url": ["https://rpc.zchains.com"]}, {"name": "MUD Chain", "chainId": 168169, "url": ["https://rpc.mud.network"]}, {"name": "Wadzchain Mainnet", "chainId": 171717, "url": ["https://rpc.wadzchain.io"]}, {"name": "Bitica Chain Mainnet", "chainId": 188710, "url": ["https://mainnet-rpc.biticablockchain.com/"]}, {"name": "Altblockscan Mainnet", "chainId": 191919, "url": ["https://rpc.altblockscan.com"]}, {"name": "R0AR Chain", "chainId": 193939, "url": ["https://rpc-r0ar.io"]}, {"name": "Zoo Mainnet", "chainId": 200200, "url": ["https://api.zoo.network"]}, {"name": "Akroma", "chainId": 200625, "url": ["https://remote.akroma.io"]}, {"name": "Bitlayer Mainnet", "chainId": 200901, "url": ["https://rpc.bitlayer.org", "https://rpc.bitlayer-rpc.com", "https://rpc.ankr.com/bitlayer", "https://rpc-bitlayer.rockx.com"]}, {"name": "Alaya Mainnet", "chainId": 201018, "url": ["https://openapi.alaya.network/rpc"]}, {"name": "Mythical Chain", "chainId": 201804, "url": ["https://chain-rpc.mythicalgames.com"]}, {"name": "Bethel Sydney", "chainId": 202202, "url": ["https://rpc-sydney.bethel.network"]}, {"name": "Blockfit", "chainId": 202424, "url": ["https://rpc.blockfitscan.io"]}, {"name": "<PERSON><PERSON>", "chainId": 202624, "url": ["https://jellie-rpc.twala.io/"]}, {"name": "X1 Network", "chainId": 204005, "url": ["https://x1-testnet.xen.network"]}, {"name": "<PERSON><PERSON>", "chainId": 210209, "url": ["https://rpc.sorian.io"]}, {"name": "PlatON Mainnet", "chainId": 210425, "url": ["https://openapi2.platon.network/rpc"]}, {"name": "<PERSON><PERSON>", "chainId": 212013, "url": ["https://rpc.heima-parachain.heima.network", "https://litentry-rpc.dwellir.com"]}, {"name": "<PERSON><PERSON>", "chainId": 220315, "url": ["http://node.masnet.ai:8545"]}, {"name": "Reapchain Mainnet", "chainId": 221230, "url": ["https://eth.reapchain.org"]}, {"name": "Hydration", "chainId": 222222, "url": ["https://rpc.hydradx.cloud", "https://hydration-rpc.n.dwellir.com", "https://rpc.helikon.io/hydradx", "https://hydration.dotters.network", "https://hydration.ibp.network", "https://rpc.cay.hydration.cloud", "https://rpc.parm.hydration.cloud", "https://rpc.roach.hydration.cloud", "https://rpc.zipp.hydration.cloud", "https://rpc.sin.hydration.cloud", "https://rpc.coke.hydration.cloud"]}, {"name": "DeepL Mainnet", "chainId": 222555, "url": ["https://rpc.deeplnetwork.org"]}, {"name": "Taf ECO Chain Mainnet", "chainId": 224168, "url": ["https://mainnet.tafchain.com/v1"]}, {"name": "CONET Mainnet", "chainId": 224400, "url": ["https://mainnet-rpc.conet.network"]}, {"name": "CONET Cancun", "chainId": 224433, "url": ["https://rpc.conet.network"]}, {"name": "Abyss Protocol", "chainId": 229772, "url": ["https://testnet.rpc.abyssprotocol.ai/"]}, {"name": "ARTIS sigma1", "chainId": 246529, "url": ["https://rpc.sigma1.artis.network"]}, {"name": "CMP-Mainnet", "chainId": 256256, "url": ["https://mainnet.block.caduceus.foundation"]}, {"name": "Nxy Area 51", "chainId": 272247, "url": ["https://nxy.social/testnet"]}, {"name": "Nxy Oasis", "chainId": 272520, "url": ["https://nxy.social/mainnet"]}, {"name": "Social Smart Chain Mainnet", "chainId": 281121, "url": ["https://socialsmartchain.digitalnext.business"]}, {"name": "Athene Parthenon", "chainId": 281123, "url": ["https://rpc.parthenon.athenescan.io"]}, {"name": "One World Chain Mainnet", "chainId": 309075, "url": ["https://mainnet-rpc.oneworldchain.org"]}, {"name": "Parex Mainnet", "chainId": 322202, "url": ["https://mainnet-rpc.parex.network"]}, {"name": "Nal Mainnet", "chainId": 328527, "url": ["https://rpc.nal.network"]}, {"name": "TTcoin Smart Chain Mainnet", "chainId": 330844, "url": ["https://mainnet-rpc.tscscan.com"]}, {"name": "Bloom Genesis Mainnet", "chainId": 333313, "url": ["https://mainnet-rpc.bloomgenesis.com"]}, {"name": "Polis Mainnet", "chainId": 333999, "url": ["https://rpc.polis.tech"]}, {"name": "UPchain Mainnet", "chainId": 336666, "url": ["https://rpc.uniport.network"]}, {"name": "Bitfinity Network Mainnet", "chainId": 355110, "url": ["https://mainnet.bitfinity.network"]}, {"name": "LAVITA Mainnet", "chainId": 360890, "url": ["https://tsub360890-eth-rpc.thetatoken.org/rpc"]}, {"name": "Digit Soul Smart Chain 2", "chainId": 363636, "url": ["https://dgs-rpc.digitsoul.co.th"]}, {"name": "Metal C-Chain", "chainId": 381931, "url": ["https://api.metalblockchain.org/ext/bc/C/rpc"]}, {"name": "Metal Tahoe C-Chain", "chainId": 381932, "url": ["https://tahoe.metalblockchain.org/ext/bc/C/rpc"]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chainId": 383353, "url": ["https://rpc.cheesechain.xyz"]}, {"name": "Tipboxcoin Mainnet", "chainId": 404040, "url": ["https://mainnet-rpc.tipboxcoin.net"]}, {"name": "Infinaeon", "chainId": 420000, "url": ["https://rpc.infinaeon.com"]}, {"name": "Vector Smart Chain", "chainId": 420042, "url": ["https://rpc.vscblockchain.org"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "chainId": 420420, "url": ["https://mainnet.kekchain.com"]}, {"name": "<PERSON><PERSON>", "chainId": 431140, "url": ["https://rpc.markr.io/ext/"]}, {"name": "Dexalot Subnet", "chainId": 432204, "url": ["https://subnets.avax.network/dexalot/mainnet/rpc"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "chainId": 440017, "url": ["https://anon-entrypoint-1.atgraphite.com"]}, {"name": "Ultra Pro Mainnet", "chainId": 473861, "url": ["https://mainnet-rpc.ultraproscan.io"]}, {"name": "OpenChain Mainnet", "chainId": 474142, "url": ["https://baas-rpc.luniverse.io:18545?lChainId=1641349324562974539"]}, {"name": "Playdapp Network", "chainId": 504441, "url": ["https://subnets.avax.network/playdappne/mainnet/rpc"]}, {"name": "EthereumFair", "chainId": 513100, "url": ["https://rpc.etherfair.org"]}, {"name": "DoCoin Community Chain", "chainId": 526916, "url": ["https://rpc.docoin.shop"]}, {"name": "<PERSON><PERSON>", "chainId": 534352, "url": ["https://rpc.scroll.io", "https://rpc.ankr.com/scroll", "https://scroll-mainnet.chainstacklabs.com", "https://scroll-rpc.publicnode.com"]}, {"name": "Shinarium Beta", "chainId": 534849, "url": ["https://rpc.shinarium.org"]}, {"name": "BeanEco SmartChain", "chainId": 535037, "url": ["https://mainnet-rpc.bescscan.io"]}, {"name": "ZERO Network", "chainId": 543210, "url": ["https://rpc.zerion.io/v1/zero"]}, {"name": "DustBoy IoT", "chainId": 555888, "url": ["https://dustboy-rpc.jibl2.com/"]}, {"name": "Ethereum <PERSON>", "chainId": 560048, "url": ["https://rpc.hoodi.ethpandaops.io"]}, {"name": "Hypra Mainnet", "chainId": 622277, "url": ["https://rpc.hypra.network", "https://rpc.rethereum.org", "https://rethereum.rpc.restratagem.com", "https://rpc.rthcentral.org", "https://hypra.rpc.thirdweb.com"]}, {"name": "Atlas", "chainId": 622463, "url": ["https://rpc.testnet.atl.network"]}, {"name": "Bear Network Chain Mainnet", "chainId": 641230, "url": ["https://brnkc-mainnet.bearnetwork.net", "https://brnkc-mainnet1.bearnetwork.net"]}, {"name": "ALL Mainnet", "chainId": 651940, "url": ["https://mainnet-rpc.alltra.global"]}, {"name": "Xai Mainnet", "chainId": 660279, "url": ["https://xai-chain.net/rpc"]}, {"name": "Conwai Mainnet", "chainId": 668668, "url": ["https://conwai.calderachain.xyz/http"]}, {"name": "Won Network", "chainId": 686868, "url": ["https://rpc.wonnetwork.org"]}, {"name": "Primea Network Mainnet", "chainId": 698369, "url": ["http://rpc.primeanetwork.com/rpc-http", "https://rpc.primeanetwork.com/rpc-https"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "chainId": 706883, "url": ["https://rpc1.fidesinnova.io"]}, {"name": "Tiltyard Mainnet Subnet", "chainId": 710420, "url": ["https://subnets.avax.network/tiltyard/mainnet/rpc"]}, {"name": "Zether Mainnet", "chainId": 715131, "url": ["https://rpc.zether.org", "https://rpc.zthscan.com"]}, {"name": "ERAM Mainnet", "chainId": 721529, "url": ["https://mainnet-rpc.eramscan.com"]}, {"name": "Ternoa", "chainId": 752025, "url": ["https://rpc-mainnet.zkevm.ternoa.network"]}, {"name": "PAYSCAN CHAIN", "chainId": 756689, "url": ["https://rpc.payscan.live"]}, {"name": "Miexs Smartchain", "chainId": 761412, "url": ["https://mainnet-rpc.miexs.com"]}, {"name": "Modularium", "chainId": 776877, "url": ["https://fraa-dancebox-3035-rpc.a.dancebox.tanssi.network"]}, {"name": "Winr Protocol Mainnet", "chainId": 777777, "url": ["https://rpc.winr.games"]}, {"name": "Oone Chain Mainnet", "chainId": 777888, "url": ["https://rpc.oonechain.com"]}, {"name": "Zebro Smart Chain", "chainId": 786786, "url": ["https://rpc.zebrocoin.app", "https://rpc1.zebrocoin.app"]}, {"name": "<PERSON><PERSON>", "chainId": 789789, "url": ["https://public.0xrpc.com/789789"]}, {"name": "OctaSpace", "chainId": 800001, "url": ["https://rpc.octa.space"]}, {"name": "zkLink Nova Mainnet", "chainId": 810180, "url": ["https://rpc.zklink.io"]}, {"name": "SG Verse Mainnet", "chainId": 812397, "url": ["https://rpc.sgverse.net/"]}, {"name": "CURVE Mainnet", "chainId": 827431, "url": ["https://mainnet-rpc.curvescan.io"]}, {"name": "4GoodNetwork", "chainId": 846000, "url": ["https://chain.deptofgood.com"]}, {"name": "Dodao", "chainId": 855456, "url": ["https://fraa-flashbox-4643-rpc.a.stagenet.tanssi.network"]}, {"name": "BlocX Mainnet", "chainId": 879151, "url": ["https://mainnet-rpc.blxscan.com/"]}, {"name": "REXX Mainnet", "chainId": 888882, "url": ["https://rpc.rexxnetwork.com"]}, {"name": "Vision - Mainnet", "chainId": 888888, "url": ["https://infragrid.v.network/ethereum/compatible"]}, {"name": "Posichain Mainnet Shard 0", "chainId": 900000, "url": ["https://api.posichain.org", "https://api.s0.posichain.org"]}, {"name": "Astria EVM Dusknet", "chainId": 912559, "url": ["https://rpc.evm.dusk-3.devnet.astria.org"]}, {"name": "O<PERSON>hen Mainnet", "chainId": 918273, "url": ["https://rpc.owshen.io"]}, {"name": "Jono12 Subnet", "chainId": 955081, "url": ["https://subnets.avax.network/jono12/testnet/rpc"]}, {"name": "Eluvio Content Fabric", "chainId": 955305, "url": ["https://host-76-74-28-226.contentfabric.io/eth/", "https://host-76-74-28-232.contentfabric.io/eth/", "https://host-76-74-29-2.contentfabric.io/eth/", "https://host-76-74-29-8.contentfabric.io/eth/", "https://host-76-74-29-34.contentfabric.io/eth/", "https://host-76-74-29-35.contentfabric.io/eth/", "https://host-154-14-211-98.contentfabric.io/eth/", "https://host-154-14-192-66.contentfabric.io/eth/", "https://host-60-240-133-202.contentfabric.io/eth/", "https://host-64-235-250-98.contentfabric.io/eth/"]}, {"name": "Treasure Topaz", "chainId": 978658, "url": ["https://rpc.topaz.treasure.lol"]}, {"name": "Forma", "chainId": 984122, "url": ["https://rpc.forma.art"]}, {"name": "Forma Sketchpad", "chainId": 984123, "url": ["https://rpc.sketchpad-1.forma.art"]}, {"name": "Ecrox Chain Mainnet", "chainId": 988207, "url": ["https://mainnet-rpc.ecroxscan.com/"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "chainId": 999999, "url": ["https://node1.amchain.net"]}, {"name": "Tiltyard Subnet", "chainId": 1127469, "url": ["https://subnets.avax.network/tiltyard/testnet/rpc"]}, {"name": "Sharecle Mainnet", "chainId": 1234567, "url": ["https://mainnet.sharecle.com"]}, {"name": "zKatana", "chainId": 1261120, "url": ["https://rpc.zkatana.gelato.digital", "https://rpc.startale.com/zkatana", "https://astar-zkatana.drpc.org"]}, {"name": "Etho Protocol", "chainId": 1313114, "url": ["https://rpc.ethoprotocol.com"]}, {"name": "Xerom", "chainId": 1313500, "url": ["https://rpc.xerom.org"]}, {"name": "Kintsugi", "chainId": 1337702, "url": ["https://rpc.kintsugi.themerge.dev"]}, {"name": "Kiln", "chainId": 1337802, "url": ["https://rpc.kiln.themerge.dev"]}, {"name": "Zhejiang", "chainId": 1337803, "url": ["https://rpc.zhejiang.ethpandaops.io"]}, {"name": "Plian Mainnet Main", "chainId": 2099156, "url": ["https://mainnet.plian.io/pchain"]}, {"name": "Coinweb BNB shard", "chainId": 2222222, "url": ["https://api-cloud.coinweb.io/eth-rpc-service/bnb"]}, {"name": "DPU Chain", "chainId": 2611555, "url": ["https://sc-rpc.dpu.ac.th"]}, {"name": "COTI", "chainId": 2632500, "url": ["https://mainnet.coti.io/rpc"]}, {"name": "Xterio Chain (ETH)", "chainId": 2702128, "url": ["https://xterio-eth.alt.technology"]}, {"name": "Sahara AI", "chainId": 3132023, "url": ["https://mainnet.saharalabs.ai"]}, {"name": "AltLayer Zero Gas Network", "chainId": 4000003, "url": ["https://zero.alt.technology"]}, {"name": "Arcadia Mainnet", "chainId": 4278608, "url": ["https://arcadia.khalani.network"]}, {"name": "Worlds Caldera", "chainId": 4281033, "url": ["https://worlds-test.calderachain.xyz/http"]}, {"name": "NumBlock Chain", "chainId": 5112023, "url": ["https://rpc-mainnet.numblock.org"]}, {"name": "Reactive <PERSON><PERSON><PERSON>", "chainId": 5318008, "url": ["https://kopli-rpc.rnk.dev"]}, {"name": "Imversed Mainnet", "chainId": 5555555, "url": ["https://jsonrpc.imversed.network", "https://ws-jsonrpc.imversed.network"]}, {"name": "Astar zKyoto", "chainId": 6038361, "url": ["https://rpc.startale.com/zkyoto", "https://rpc.zkyoto.gelato.digital"]}, {"name": "Safe(AnWang) Mainnet", "chainId": 6666665, "url": ["https://rpc.anwang.com"]}, {"name": "Saakuru Mainnet", "chainId": 7225878, "url": ["https://rpc.saakuru.network"]}, {"name": "OpenVessel", "chainId": 7355310, "url": ["https://mainnet-external.openvessel.io"]}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 7762959, "url": ["https://mewapi.musicoin.tw"]}, {"name": "<PERSON><PERSON>", "chainId": 7777777, "url": ["https://rpc.zora.energy/"]}, {"name": "Plian Mainnet Subchain 1", "chainId": 8007736, "url": ["https://mainnet.plian.io/child_0"]}, {"name": "Fhenix Helium", "chainId": 8008135, "url": ["https://api.helium.fhenix.zone"]}, {"name": "Hokum", "chainId": 8080808, "url": ["https://mainnet.hokum.gg"]}, {"name": "HAPchain", "chainId": 8794598, "url": ["https://jsonrpc.hap.land"]}, {"name": "XCAP", "chainId": 9322252, "url": ["https://xcap-mainnet.relay.xcap.network/znzvh2ueyvm2yts5fv5gnul395jbkfb2/rpc1"]}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 9322253, "url": ["https://xcap-milvine.relay.xcap.network/zj5l55ftsgi027kz4nf14vs8d89inego/rpc1"]}, {"name": "Fluence", "chainId": 9999999, "url": ["https://rpc.mainnet.fluence.dev/"]}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 10101010, "url": ["https://mainnet-rpc.soverun.com"]}, {"name": "AlienX Mainnet", "chainId": 10241024, "url": ["https://rpc.alienxchain.io/http"]}, {"name": "Lummio Network", "chainId": 12020498, "url": ["https://rpc.lummio.net"]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chainId": 13371337, "url": ["https://churchill-rpc.pepchain.io"]}, {"name": "Andus<PERSON>in Mainnet", "chainId": 14288640, "url": ["https://rpc.anduschain.io/rpc"]}, {"name": "Privix Chain Mainnet", "chainId": 16969696, "url": ["https://mainnet-rpc.privixchain.xyz/"]}, {"name": "Mande Network Mainnet", "chainId": 18071918, "url": ["https://mande-mainnet.public.blastapi.io"]}, {"name": "IOLite", "chainId": 18289463, "url": ["https://net.iolite.io"]}, {"name": "DeepBrainChain Mainnet", "chainId": 19880818, "url": ["https://rpc.dbcwallet.io"]}, {"name": "SmartMesh Mainnet", "chainId": 20180430, "url": ["https://jsonapi1.smartmesh.cn"]}, {"name": "quarkblockchain", "chainId": 20181205, "url": ["https://hz.rpc.qkiscan.cn", "https://jp.rpc.qkiscan.io", "https://rpc1.qkiscan.io", "https://rpc2.qkiscan.io", "https://rpc3.qkiscan.io"]}, {"name": "Pego Network", "chainId": 20201022, "url": ["https://pegorpc.com", "https://node1.pegorpc.com", "https://node2.pegorpc.com", "https://node3.pegorpc.com"]}, {"name": "DBK Chain", "chainId": 20240603, "url": ["https://rpc.mainnet.dbkchain.io"]}, {"name": "Xphere Mainnet", "chainId": 20250217, "url": ["https://en-bkk.x-phere.com"]}, {"name": "Vcitychain Mainnet", "chainId": 20250825, "url": ["https://mainnet-rpc.vcity.app"]}, {"name": "ETP Mainnet", "chainId": 20256789, "url": ["https://rpc.etpscan.xyz"]}, {"name": "Corn", "chainId": 21000000, "url": ["https://mainnet.corn-rpc.com", "https://rpc.ankr.com/corn_maizenet", "https://maizenet-rpc.usecorn.com"]}, {"name": "Excelon Mainnet", "chainId": 22052002, "url": ["https://edgewallet1.xlon.org/"]}, {"name": "Excoincial Chain Mainnet", "chainId": 27082022, "url": ["https://rpc.exlscan.com"]}, {"name": "Auxilium Network Mainnet", "chainId": 28945486, "url": ["https://rpc.auxilium.global"]}, {"name": "Flachain Mainnet", "chainId": 29032022, "url": ["https://flachain.flaexchange.top/"]}, {"name": "citronus-citro", "chainId": 34949059, "url": ["https://rpc.citro-testnet.t.raas.gelato.cloud"]}, {"name": "Joys Digital Mainnet", "chainId": 35855456, "url": ["https://node.joys.digital"]}, {"name": "Kingdom Chain", "chainId": 39916801, "url": ["https://kingdomchain.observer/rpc"]}, {"name": "Deviant <PERSON>", "chainId": 52027071, "url": ["https://rpc.devianttoken.net"]}, {"name": "Aquachain", "chainId": 61717561, "url": ["https://c.onical.org", "https://tx.aquacha.in/api"]}, {"name": "SX Toronto Rollup", "chainId": 79479957, "url": ["https://rpc.sx-rollup-testnet.t.raas.gelato.cloud"]}, {"name": "T.E.A.M Blockchain", "chainId": 88888888, "url": ["https://rpc.teamblockchain.team"]}, {"name": "Reya Cronos", "chainId": 89346162, "url": ["https://rpc.reya-cronos.gelato.digital"]}, {"name": "Polygon Blackberry", "chainId": 94204209, "url": ["https://rpc.polygon-blackberry.gelato.digital"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "chainId": 97912060, "url": ["https://rpc.chadchain.org"]}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 100000000, "url": ["https://rpc.ethos.cool"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "chainId": 108160679, "url": ["https://evm.orai.io"]}, {"name": "OP Celestia <PERSON>", "chainId": 123420111, "url": ["https://rpc.opcelestia-raspberry.gelato.digital"]}, {"name": "Gather Mainnet Network", "chainId": 192837465, "url": ["https://mainnet.gather.network"]}, {"name": "Kanazawa", "chainId": 222000222, "url": ["https://testnet-rpc.meld.com"]}, {"name": "Neon EVM Mainnet", "chainId": 245022934, "url": ["https://neon-proxy-mainnet.solana.p2p.org", "https://neon-evm.drpc.org"]}, {"name": "Flame", "chainId": 253368190, "url": ["https://rpc.flame.astria.org"]}, {"name": "Razor <PERSON> Chain", "chainId": 278611351, "url": ["https://mainnet.skalenodes.com/v1/turbulent-unique-scheat"]}, {"name": "OneLedger Mainnet", "chainId": 311752642, "url": ["https://mainnet-rpc.oneledger.network"]}, {"name": "<PERSON>d", "chainId": 333000333, "url": ["https://rpc-1.meld.com"]}, {"name": "<PERSON><PERSON>", "chainId": 420420419, "url": ["https://asset-hub-eth-rpc.polkadot.io"]}, {"name": "Westend Asset Hub", "chainId": 420420421, "url": ["https://westend-asset-hub-eth-rpc.polkadot.io"]}, {"name": "Degen Chain", "chainId": 666666666, "url": ["https://rpc.degen.tips"]}, {"name": "Tron Mainnet", "chainId": 728126428, "url": ["https://rpc.ankr.com/tron_jsonrpc", "https://api.trongrid.io/jsonrpc"]}, {"name": "Ancient8", "chainId": 888888888, "url": ["https://rpc.ancient8.gg"]}, {"name": "PTCESCAN Mainnet", "chainId": 889910246, "url": ["https://rpc.ptcscan.io"]}, {"name": "Lu<PERSON>net", "chainId": 994873017, "url": ["https://mainnet-rpc.lumia.org"]}, {"name": "IPOS Network", "chainId": 1122334455, "url": ["https://rpc.iposlab.com", "https://rpc2.iposlab.com"]}, {"name": "CyberdeckNet", "chainId": 1146703430, "url": ["http://cybeth1.cyberdeck.eu:8545"]}, {"name": "HUMAN Protocol", "chainId": 1273227453, "url": ["https://mainnet.skalenodes.com/v1/wan-red-ain"]}, {"name": "OFFICIAL VASYL", "chainId": 1278060416, "url": ["https://rpc.official-vasyl.network"]}, {"name": "Aurora Mainnet", "chainId": 1313161554, "url": ["https://mainnet.aurora.dev"]}, {"name": "PowerGold", "chainId": 1313161560, "url": ["https://powergold.aurora.dev"]}, {"name": "Turbo", "chainId": 1313161567, "url": ["https://rpc-0x4e45415f.aurora-cloud.dev"]}, {"name": "Tuxappcoin", "chainId": 1313161573, "url": ["https://rpc-0x4e454165.aurora-cloud.dev"]}, {"name": "SKALE Titan Hub", "chainId": 1350216234, "url": ["https://mainnet.skalenodes.com/v1/parallel-stormy-spica"]}, {"name": "RARI Chain Mainnet", "chainId": 1380012617, "url": ["https://rari.calderachain.xyz/http"]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chainId": 1380996178, "url": ["https://rpc.raptorchain.io/web3"]}, {"name": "SKALE Nebula Hub", "chainId": 1482601649, "url": ["https://mainnet.skalenodes.com/v1/green-giddy-denebola"]}, {"name": "GPT Mainnet", "chainId": 1511670449, "url": ["https://rpc.gptprotocol.io"]}, {"name": "SKALE Calypso Hub", "chainId": 1564830818, "url": ["https://mainnet.skalenodes.com/v1/honorable-steel-rasalhague"]}, {"name": "Harmony Mainnet Shard 0", "chainId": 1666600000, "url": ["https://api.harmony.one", "https://api.s0.t.hmny.io"]}, {"name": "Harmony Mainnet Shard 1", "chainId": 1666600001, "url": ["https://api.s1.t.hmny.io", "https://harmony-1.drpc.org"]}, {"name": "Harmony Mainnet Shard 2", "chainId": 1666600002, "url": ["https://api.s2.t.hmny.io"]}, {"name": "Harmony Mainnet Shard 3", "chainId": 1666600003, "url": ["https://api.s3.t.hmny.io"]}, {"name": "WITNESS CHAIN", "chainId": 1702448187, "url": ["https://sequencer.witnesschain.com"]}, {"name": "DataHopper", "chainId": 2021121117, "url": ["https://23.92.21.121:8545"]}, {"name": "SKALE Europa Hub", "chainId": 2046399126, "url": ["https://mainnet.skalenodes.com/v1/elated-tan-skat"]}, {"name": "Accumulate Kermit", "chainId": 2478899481, "url": ["https://kermit.accumulatenetwork.io/eth"]}, {"name": "<PERSON><PERSON>", "chainId": 2494104990, "url": ["https://api.shasta.trongrid.io/jsonrpc"]}, {"name": "Pirl", "chainId": 3125659152, "url": ["https://wallrpc.pirl.io"]}, {"name": "Mekong", "chainId": 7078815900, "url": ["https://rpc.mekong.ethpandaops.io"]}, {"name": "ONFA Chain", "chainId": 8691942025, "url": ["https://rpc.onfa.io", "https://rpc.onfachain.com"]}, {"name": "Palm", "chainId": 11297108109, "url": ["https://palm-mainnet.public.blastapi.io"]}, {"name": "Arbitrum Blueberry", "chainId": 88153591557, "url": ["https://rpc.arb-blueberry.gelato.digital"]}, {"name": "Alphabet Mainnet", "chainId": 111222333444, "url": ["https://londonpublic.alphabetnetwork.org", "https://main-rpc.com"]}, {"name": "Fluence Stage", "chainId": 123420000220, "url": ["https://rpc.stage.fluence.dev"]}, {"name": "PIN", "chainId": 123420000558, "url": ["https://rpc.pin.t.raas.gelato.cloud"]}, {"name": "volmex", "chainId": 123420000588, "url": ["https://rpc.volmex.t.raas.gelato.cloud"]}, {"name": "Basecamp", "chainId": 123420001114, "url": ["https://rpc.basecamp.t.raas.gelato.cloud"]}, {"name": "Ntity Mainnet", "chainId": 197710212030, "url": ["https://rpc.ntity.io"]}, {"name": "Zeniq", "chainId": 383414847825, "url": ["https://api.zeniq.network"]}, {"name": "PDC Mainnet", "chainId": 666301171999, "url": ["https://mainnet.ipdc.io/"]}, {"name": "SmartPay Mobile Money", "chainId": 666301179999, "url": ["https://network.uat.smartmoneyewallet.com/"]}, {"name": "Molereum Network", "chainId": 6022140761023, "url": ["https://molereum.jdubedition.com"]}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 428962654539583, "url": ["https://jsonrpc-yominet-1.anvil.asia-southeast.initia.xyz"]}, {"name": "DCHAIN", "chainId": 2716446429837000, "url": ["https://dchain-2716446429837000-1.jsonrpc.sagarpc.io"]}]