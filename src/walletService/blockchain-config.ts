// Mapping of chain IDs to blockchain types for multi-blockchain support
export const CHAIN_ID_TO_BLOCKCHAIN_TYPE: { [key: number]: string } = {
  // Bitcoin-like chains (would need Bitcoin RPC endpoints)
  // Note: Bitcoin doesn't have a standard chain ID like EVM chains
  
  // Ethereum and EVM-compatible chains (use Ethereum address format)
  1: "ETHEREUM",     // Ethereum Mainnet
  10: "ETHEREUM",    // Optimism
  25: "ETHEREUM",    // Cronos
  56: "ETHEREUM",    // BNB Smart Chain
  137: "ETHEREUM",   // Polygon
  250: "ETHEREUM",   // Fantom
  324: "ETHEREUM",   // zkSync Era
  42161: "ETHEREUM", // Arbitrum One
  43114: "ETHEREUM", // Avalanche C-Chain
  
  // Add more EVM chains - most chains in your RPC list are EVM-compatible
  2: "ETHEREUM",     // Expanse Network
  7: "ETHEREUM",     // <PERSON><PERSON><PERSON>n
  8: "ETHEREUM",     // Ubiq
  11: "ETHEREUM",    // Metadium Mainnet
  14: "ETHEREUM",    // Flare Mainnet
  15: "ETHEREUM",    // Diode Prenet
  17: "ETHEREUM",    // ThaiChain 2.0 ThaiFi
  19: "ETHEREUM",    // Songbird Canary-Network
  20: "ETHEREUM",    // Elastos Smart Chain
  24: "ETHEREUM",    // KardiaChain Mainnet
  27: "ETHEREUM",    // ShibaChain
  29: "ETHEREUM",    // Genesis L1
  30: "ETHEREUM",    // Rootstock Mainnet
  33: "ETHEREUM",    // GoodData Mainnet
  34: "ETHEREUM",    // SecureChain Mainnet
  35: "ETHEREUM",    // TBWG Chain
  36: "ETHEREUM",    // Dxchain Mainnet
  37: "ETHEREUM",    // Xpla Mainnet
  38: "ETHEREUM",    // Valorbit
  39: "ETHEREUM",    // U2U Solaris Mainnet
  40: "ETHEREUM",    // Telos EVM Mainnet
  42: "ETHEREUM",    // LUKSO Mainnet
  44: "ETHEREUM",    // Crab Network
  46: "ETHEREUM",    // Darwinia Network
  47: "ETHEREUM",    // Acria IntelliChain
  48: "ETHEREUM",    // Ennothem Mainnet Proterozoic
  50: "ETHEREUM",    // XDC Network
  51: "ETHEREUM",    // XDC Apothem Network
  52: "ETHEREUM",    // CoinEx Smart Chain Mainnet
  54: "ETHEREUM",    // Openpiece Mainnet
  55: "ETHEREUM",    // Zyx Mainnet
  57: "ETHEREUM",    // Syscoin Mainnet
  58: "ETHEREUM",    // Ontology Mainnet
  59: "ETHEREUM",    // EOS EVM Legacy
  60: "ETHEREUM",    // GoChain
  61: "ETHEREUM",    // Ethereum Classic
  64: "ETHEREUM",    // Ellaism
  66: "ETHEREUM",    // OKXChain Mainnet
  68: "ETHEREUM",    // SoterOne Mainnet
  70: "ETHEREUM",    // Hoo Smart Chain
  73: "ETHEREUM",    // FNCY
  74: "ETHEREUM",    // IDChain Mainnet
  75: "ETHEREUM",    // Decimal Smart Chain Mainnet
  76: "ETHEREUM",    // Mix
  77: "ETHEREUM",    // POA Network Sokol
  78: "ETHEREUM",    // PrimusChain mainnet
  79: "ETHEREUM",    // Zenith Mainnet
  80: "ETHEREUM",    // GeneChain
  81: "ETHEREUM",    // Japan Open Chain Mainnet
  82: "ETHEREUM",    // Meter Mainnet
  86: "ETHEREUM",    // GateChain Mainnet
  87: "ETHEREUM",    // Nova Network
  88: "ETHEREUM",    // Viction
  90: "ETHEREUM",    // Garizon Stage0
  91: "ETHEREUM",    // Garizon Stage1
  92: "ETHEREUM",    // Garizon Stage2
  93: "ETHEREUM",    // Garizon Stage3
  94: "ETHEREUM",    // SwissDLT
  95: "ETHEREUM",    // CamDL Mainnet
  96: "ETHEREUM",    // KUB Mainnet
  98: "ETHEREUM",    // Six Protocol
  99: "ETHEREUM",    // POA Network Core
  100: "ETHEREUM",   // Gnosis
  101: "ETHEREUM",   // EtherInc
  103: "ETHEREUM",   // WorldLand Mainnet
  106: "ETHEREUM",   // Velas EVM Mainnet
  108: "ETHEREUM",   // ThunderCore Mainnet
  109: "ETHEREUM",   // Shibarium
  111: "ETHEREUM",   // EtherLite Chain
  112: "ETHEREUM",   // Coinbit Mainnet
  113: "ETHEREUM",   // Dehvo
  117: "ETHEREUM",   // Uptick Mainnet
  119: "ETHEREUM",   // ENULS Mainnet
  121: "ETHEREUM",   // Realchain Mainnet
  122: "ETHEREUM",   // Fuse Mainnet
  123: "ETHEREUM",   // Fuse Sparknet
  124: "ETHEREUM",   // Decentralized Web Mainnet
  126: "ETHEREUM",   // OYchain Mainnet
  128: "ETHEREUM",   // Huobi ECO Chain Mainnet
  129: "ETHEREUM",   // Innovator Chain
  130: "ETHEREUM",   // Unichain
  132: "ETHEREUM",   // Namefi Chain Mainnet
  134: "ETHEREUM",   // iExec Sidechain
  136: "ETHEREUM",   // Deamchain Mainnet
  138: "ETHEREUM",   // Defi Oracle Meta Mainnet
  139: "ETHEREUM",   // WoopChain Mainnet
  140: "ETHEREUM",   // Eternal Mainnet
  142: "ETHEREUM",   // DAX CHAIN
  144: "ETHEREUM",   // PHI Network v2
  146: "ETHEREUM",   // Sonic Mainnet
  147: "ETHEREUM",   // Flag Mainnet
  148: "ETHEREUM",   // ShimmerEVM
  151: "ETHEREUM",   // Redbelly Network Mainnet
  157: "ETHEREUM",   // Puppynet
  158: "ETHEREUM",   // Roburna Mainnet
  160: "ETHEREUM",   // Armonia Eva Chain Mainnet
  163: "ETHEREUM",   // Lightstreams Mainnet
  166: "ETHEREUM",   // Omni
  168: "ETHEREUM",   // AIOZ Network
  169: "ETHEREUM",   // Manta Pacific Mainnet
  171: "ETHEREUM",   // CO2e Chain
  175: "ETHEREUM",   // OTC
  176: "ETHEREUM",   // DC Mainnet
  177: "ETHEREUM",   // HashKey Chain
  179: "ETHEREUM",   // ABEY Mainnet
  180: "ETHEREUM",   // AME Chain Mainnet
  181: "ETHEREUM",   // Waterfall Network
  182: "ETHEREUM",   // IOST Mainnet
  183: "ETHEREUM",   // Ethernity
  185: "ETHEREUM",   // Mint Mainnet
  186: "ETHEREUM",   // Seele Mainnet
  187: "ETHEREUM",   // Dojima
  188: "ETHEREUM",   // BMC Mainnet
  190: "ETHEREUM",   // CMDAO BBQ Chain
  191: "ETHEREUM",   // FileFileGo
  193: "ETHEREUM",   // Crypto Emergency
  194: "ETHEREUM",   // firachain
  196: "ETHEREUM",   // X Layer Mainnet
  198: "ETHEREUM",   // Bitchain Mainnet
  199: "ETHEREUM",   // BitTorrent Chain Mainnet
  200: "ETHEREUM",   // Arbitrum on xDai
  203: "ETHEREUM",   // WowChain Mainnet
  204: "ETHEREUM",   // opBNB Mainnet
  205: "ETHEREUM",   // EKAASH
  207: "ETHEREUM",   // VinuChain Network
  208: "ETHEREUM",   // Structx Mainnet
  210: "ETHEREUM",   // Bitnet
  211: "ETHEREUM",   // Freight Trust Network
  212: "ETHEREUM",   // MAPO Makalu
  213: "ETHEREUM",   // B2 Hub Mainnet
  214: "ETHEREUM",   // Shinarium Mainnet
  215: "ETHEREUM",   // IDN Mainnet
  217: "ETHEREUM",   // SiriusNet V2
  218: "ETHEREUM",   // SoterOne Mainnet old
  221: "ETHEREUM",   // BlockEx Mainnet
  222: "ETHEREUM",   // Permission
  223: "ETHEREUM",   // B2 Mainnet
  225: "ETHEREUM",   // LACHAIN Mainnet
  227: "ETHEREUM",   // Prom
  228: "ETHEREUM",   // Mind Network Mainnet
  230: "ETHEREUM",   // SwapDEX
  232: "ETHEREUM",   // Lens
  238: "ETHEREUM",   // Blast Mainnet
  242: "ETHEREUM",   // Plinga Mainnet
  246: "ETHEREUM",   // Energy Web Chain
  247: "ETHEREUM",   // ChooChain
  248: "ETHEREUM",   // Oasys Mainnet
  
  // Add more chains as needed...
  // For now, we'll default most chains to ETHEREUM since they're EVM-compatible
};

/**
 * Get blockchain type for a given chain ID
 */
export function getBlockchainType(chainId: number): string {
  return CHAIN_ID_TO_BLOCKCHAIN_TYPE[chainId] || "ETHEREUM"; // Default to Ethereum for EVM chains
}

/**
 * Check if a chain ID is supported for multi-blockchain wallet generation
 */
export function isSupportedChain(chainId: number): boolean {
  return chainId in CHAIN_ID_TO_BLOCKCHAIN_TYPE || true; // For now, support all EVM chains
}
