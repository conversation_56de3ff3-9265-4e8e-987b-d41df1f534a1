const bip39 = require("bip39");
const hdkey = require("hdkey");
const ethWallet = require("ethereumjs-wallet").default;

// Blockchain configuration with derivation paths and coin types
export const BLOCKCHAIN_CONFIG = {
  // Ethereum and EVM-compatible chains (these work with your current RPC endpoints)
  ETHEREUM: {
    coinType: 60,
    derivationPath: "m/44'/60'/0'/0",
    addressType: "ETHEREUM",
    name: "Ethereum",
  },
  // Additional EVM chains with different derivation paths for testing
  ETHEREUM_LEGACY: {
    coinType: 60,
    derivationPath: "m/44'/60'/0'/0",
    addressType: "ETHEREUM",
    name: "Ethereum Legacy",
  },
  ETHEREUM_LEDGER: {
    coinType: 60,
    derivationPath: "m/44'/60'/0'/0",
    addressType: "ETHEREUM",
    name: "Ethereum Ledger",
  },
  // BSC uses same derivation as Ethereum
  BSC: {
    coinType: 60,
    derivationPath: "m/44'/60'/0'/0",
    addressType: "ETHEREUM",
    name: "Binance Smart Chain",
  },
  // Polygon uses same derivation as Ethereum
  POLYGON: {
    coinType: 60,
    derivationPath: "m/44'/60'/0'/0",
    addressType: "ETHEREUM",
    name: "Polygon",
  },
  // Avalanche uses same derivation as Ethereum
  AVALANCHE: {
    coinType: 60,
    derivationPath: "m/44'/60'/0'/0",
    addressType: "ETHEREUM",
    name: "Avalanche",
  },
  // Fantom uses same derivation as Ethereum
  FANTOM: {
    coinType: 60,
    derivationPath: "m/44'/60'/0'/0",
    addressType: "ETHEREUM",
    name: "Fantom",
  },
  // Arbitrum uses same derivation as Ethereum
  ARBITRUM: {
    coinType: 60,
    derivationPath: "m/44'/60'/0'/0",
    addressType: "ETHEREUM",
    name: "Arbitrum",
  },
};

/**
 * Generates Ethereum address from public key
 */
function generateEthereumAddress(publicKey: Buffer): string {
  const wallet = ethWallet.fromPublicKey(publicKey, true);
  return `0x${wallet.getAddress().toString("hex")}`;
}

/**
 * Generates address based on blockchain type
 */
function generateAddressForBlockchain(
  publicKey: Buffer,
  _blockchainType: string
): string {
  // All our supported blockchains are EVM-compatible and use Ethereum address format
  return generateEthereumAddress(publicKey);
}

/**
 * Generates wallets for multiple blockchain types from a single seed phrase
 */
export function generateMultiBlockchainWalletsFromSeed() {
  // Step 1: Generate 12-word mnemonic
  const mnemonic = bip39.generateMnemonic();

  // Step 2: Convert mnemonic to seed
  const seed = bip39.mnemonicToSeedSync(mnemonic);

  // Step 3: Create HD root key from seed
  const root = hdkey.fromMasterSeed(seed);

  const walletsByBlockchain: any = {};

  // Step 4: Generate wallets for each blockchain type
  Object.entries(BLOCKCHAIN_CONFIG).forEach(([key, config]) => {
    const wallets = [];

    for (let i = 0; i < 10; i++) {
      const derivationPath = `${config.derivationPath}/${i}`;
      const addrNode = root.derive(derivationPath);
      const privateKey = addrNode.privateKey.toString("hex");
      const publicKey = addrNode.publicKey;

      let address: string;
      try {
        address = generateAddressForBlockchain(publicKey, config.addressType);
      } catch (error) {
        console.error(`Error generating address for ${config.name}:`, error);
        // Fallback to Ethereum-style address for compatibility
        address = generateEthereumAddress(publicKey);
      }

      wallets.push({
        index: i,
        privateKey,
        address,
        derivationPath,
        blockchainType: config.name,
        coinType: config.coinType,
      });
    }

    walletsByBlockchain[key] = {
      config,
      wallets,
    };
  });

  return { mnemonic, walletsByBlockchain };
}

/**
 * Original function for backward compatibility - generates only Ethereum wallets
 */
export function generateMultipleWalletsFromSeed() {
  // Step 1: Generate 12-word mnemonic
  const mnemonic = bip39.generateMnemonic();

  // Step 2: Convert mnemonic to seed
  const seed = bip39.mnemonicToSeedSync(mnemonic);

  // Step 3: Create HD root key from seed
  const root = hdkey.fromMasterSeed(seed);

  // Step 4: Derive multiple wallets from m/44'/60'/0'/0/i
  const wallets = [];

  for (let i = 0; i < 10; i++) {
    const addrNode = root.derive(`m/44'/60'/0'/0/${i}`);
    const wallet = ethWallet.fromPrivateKey(addrNode.privateKey);
    const privateKey = wallet.getPrivateKey().toString("hex");
    const address = wallet.getAddress().toString("hex");

    wallets.push({
      index: i,
      privateKey,
      address: `0x${address}`, // Lowercase '0x' is standard
    });
  }
  // console.log("--", mnemonic, wallets);
  return { mnemonic, wallets };
}
