/**
 * Express application for starting the wallet service server.
 * @module Server
 */
import express from "express";
import { walletManager } from "../walletService";
import { MongoClient } from "mongodb";
import { dbUrl } from "../utils";
import net from "net";

const app = express();
app.use(express.json());
let port = 1234;
const client = new MongoClient(dbUrl);

/**
 * Check if a port is available
 * @param {number} port - Port to check
 * @returns {Promise<boolean>} - True if port is available, false otherwise
 */
const isPortAvailable = (port: number): Promise<boolean> => {
  return new Promise((resolve) => {
    const server = net.createServer();

    server.listen(port, () => {
      server.once("close", () => {
        resolve(true);
      });
      server.close();
    });

    server.on("error", () => {
      resolve(false);
    });
  });
};

/**
 * Find an available port starting from the given port
 * @param {number} startPort - Starting port to check
 * @param {number} maxPort - Maximum port to check (default: 65535)
 * @returns {Promise<number>} - Available port number
 */
const findAvailablePort = async (
  startPort: number,
  maxPort: number = 65535
): Promise<number> => {
  for (let port = startPort; port <= maxPort; port++) {
    if (await isPortAvailable(port)) {
      return port;
    }
  }
  throw new Error(
    `No available port found between ${startPort} and ${maxPort}`
  );
};

/**
 * GET endpoint to fetch all wallet data from MongoDB
 * @route GET /api/process-wallets
 * @returns {Object} JSON response containing wallet data
 * @returns {boolean} success - Whether the request was successful
 * @returns {Array} data - Array of wallet objects containing balance information
 */
app.get("/api/process-wallets", async (req, res) => {
  try {
    await client.connect();
    const db = client.db("wallet");
    const collection = db.collection("balance");

    const wallets = await collection.find({}).toArray();

    return res.status(200).json({
      success: true,
      data: wallets,
    });
  } catch (error: any) {
    console.error("Error fetching wallets:", error);
    return res.status(500).json({
      success: false,
      message: error.message || "Internal server error",
    });
  } finally {
    await client.close();
  }
});

/**
 * POST endpoint to start wallet processing
 * @route POST /api/process-wallets
 * @returns {Object} JSON response indicating success or failure
 */
app.post("/api/process-wallets", async (req, res) => {
  try {
    // Start the multi-blockchain wallet processing
    walletManager.main();

    return res.status(200).json({
      success: true,
      message: "Multi-blockchain wallet processing started successfully",
    });
  } catch (error: any) {
    console.error("API Error:", error);
    return res.status(500).json({
      success: false,
      message: error.message || "Internal server error",
    });
  }
});

/**
 * Root route that redirects to the wallet data endpoint
 * @route GET /
 * @redirects {string} /api/process-wallets
 */
app.get("/", (req, res) => {
  res.redirect("/api/process-wallets");
});

/**
 * Starts the Express server with automatic port detection and triggers the wallet manager's main process.
 * @function startServer
 * @listens {number} port - The port number the server will listen on
 */
const startServer = async () => {
  try {
    // Find an available port starting from the default port
    const availablePort = await findAvailablePort(port);

    if (availablePort !== port) {
      console.log(
        `Port ${port} is already in use. Using port ${availablePort} instead.`
      );
    }

    app.listen(availablePort, () => {
      console.log(`Server running at http://localhost:${availablePort}`);
      console.log(
        `Send POST request to http://localhost:${availablePort}/api/process-wallets to start wallet processing`
      );
    });
  } catch (error) {
    console.error("Failed to start server:", error);
    process.exit(1);
  }
};

// Start the server
startServer();
