import { dbUrl } from "../utils";
import { walletManager } from "../walletService";
import { MongoClient } from "mongodb";
const client = new MongoClient(dbUrl);

export default async function handler(req: any, res: any) {
  // Handle GET request to fetch wallet data
  if (req.method === "GET") {
    try {
      await client.connect();
      const db = client.db("wallet");
      const collection = db.collection("balance");

      const wallets = await collection.find({}).toArray();

      return res.status(200).json({
        success: true,
        data: wallets,
      });
    } catch (error: any) {
      console.error("Error fetching wallets:", error);
      return res.status(500).json({
        success: false,
        message: error.message || "Internal server error",
      });
    } finally {
      await client.close();
    }
  }

  // Handle POST request for processing wallets
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  try {
    // Start the multi-blockchain wallet processing by default
    await walletManager.main();

    return res.status(200).json({
      success: true,
      message: "Multi-blockchain wallet processing started successfully",
    });
  } catch (error: any) {
    console.error("API Error:", error);
    return res.status(500).json({
      success: false,
      message: error.message || "Internal server error",
    });
  }
}
