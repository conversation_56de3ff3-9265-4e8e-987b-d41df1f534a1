# Multi-Blockchain Seed Phrase Wallet Generator

## Overview

This enhanced version of your seed phrase wallet generator now supports checking the same seed phrase across multiple blockchain networks. The system generates wallets for different blockchain types from a single seed phrase and checks balances across all your configured RPC endpoints.

## Key Features

✅ **Single Seed Phrase, Multiple Blockchains**: Generate wallets for multiple blockchain types from one seed phrase
✅ **EVM-Compatible Chains**: Full support for Ethereum and all EVM-compatible chains
✅ **Automatic Balance Checking**: Check balances across all configured blockchain networks
✅ **Database Storage**: Store results with blockchain type information
✅ **Backward Compatibility**: Original functionality remains unchanged

## How It Works

### 1. Seed Phrase Generation
- Generates a single 12-word BIP39 mnemonic seed phrase
- Uses this seed to derive wallets for multiple blockchain types

### 2. Multi-Blockchain Wallet Derivation
The system generates wallets for these blockchain types:
- **Ethereum**: Standard Ethereum wallets (m/44'/60'/0'/0/i)
- **Binance Smart Chain**: BSC-compatible wallets
- **Polygon**: Polygon network wallets
- **Avalanche**: Avalanche C-Chain wallets
- **Fantom**: Fantom network wallets
- **Arbitrum**: Arbitrum network wallets

### 3. Address Format
All supported blockchains use Ethereum-style addresses (0x...) since they are EVM-compatible.

## Usage

### API Endpoint

**POST** `/api/process-wallets`

#### For Multi-Blockchain Mode:
```json
{
  "multiBlockchain": true
}
```

#### For Original Mode (Ethereum only):
```json
{
  "multiBlockchain": false
}
```

### Example Usage

```bash
# Start multi-blockchain wallet processing
curl -X POST http://localhost:3000/api/process-wallets \
  -H "Content-Type: application/json" \
  -d '{"multiBlockchain": true}'

# Start original wallet processing (Ethereum only)
curl -X POST http://localhost:3000/api/process-wallets \
  -H "Content-Type: application/json" \
  -d '{"multiBlockchain": false}'
```

## Database Schema

When using multi-blockchain mode, the database stores additional information:

```javascript
{
  chain: "Ethereum Mainnet",           // Network name
  chainId: 1,                          // Chain ID
  blockchainType: "Ethereum",          // Blockchain type
  derivationPath: "m/44'/60'/0'/0/0",  // Full derivation path
  coinType: 60,                        // BIP44 coin type
  privateKey: "...",                   // Private key
  mnemonic: "...",                     // Seed phrase
  walletAddress: "0x...",              // Wallet address
  balance: 0.0,                        // Balance in native token
  timestamp: "2024-01-01T00:00:00Z"    // Timestamp
}
```

## Configuration

### Blockchain Types

The system supports these blockchain configurations:

```typescript
BLOCKCHAIN_CONFIG = {
  ETHEREUM: {
    coinType: 60,
    derivationPath: "m/44'/60'/0'/0",
    addressType: "ETHEREUM",
    name: "Ethereum"
  },
  BSC: {
    coinType: 60,
    derivationPath: "m/44'/60'/0'/0", 
    addressType: "ETHEREUM",
    name: "Binance Smart Chain"
  },
  // ... more configurations
}
```

### RPC Endpoints

Your existing `blockchain-rpc-endpoints.json` file contains 200+ RPC endpoints. The system will:
1. Generate wallets for the appropriate blockchain type
2. Check balances using the corresponding RPC endpoint
3. Store results with blockchain type information

## Benefits

### 1. Comprehensive Coverage
- Check the same seed phrase across multiple blockchain networks
- Discover funds that might exist on different chains

### 2. Efficiency
- Single seed phrase generates wallets for all supported blockchains
- Parallel processing across multiple networks

### 3. Flexibility
- Can switch between original mode and multi-blockchain mode
- Backward compatible with existing functionality

## Example Output

When a wallet with balance is found:

```
FOUND BALANCE!
Mnemonic: abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon about
Private Key: 0x...
Address: ******************************************
Balance: 1.5 ETH
Network: Ethereum Mainnet (Ethereum)
Derivation Path: m/44'/60'/0'/0/0
```

## Testing

Run the test script to verify multi-blockchain functionality:

```bash
npm run build
node dist/test-multi-blockchain.js
```

This will generate a sample seed phrase and show wallets for all supported blockchain types.

## Next Steps

1. **Start the Service**: Use the API endpoint with `multiBlockchain: true`
2. **Monitor Results**: Check the database for wallets with balances
3. **Extend Support**: Add more blockchain types as needed

The system is designed to be extensible - you can easily add support for additional blockchain types by updating the `BLOCKCHAIN_CONFIG` object.
