{"name": "seed-wallet", "version": "1.0.0", "description": "", "main": "/dist/Server/index.js", "scripts": {"build": "tsc", "start": "npm run build && node dist/Server/index.js", "dev": "npm run build && node dist/Server/index.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"bip39": "^3.1.0", "ethereumjs-wallet": "^1.0.2", "ethers": "^5.7.2", "events": "^3.3.0", "express": "^4.19.2", "hdkey": "^2.1.0", "mongodb": "^6.1.0"}, "devDependencies": {"@types/express": "^4.17.21", "@types/node": "^16.11.7", "ts-node": "^10.9.1", "typescript": "^4.5.4"}, "engines": {"node": ">=16"}}