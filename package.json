{"name": "seed-wallet", "version": "1.0.0", "description": "", "main": "/dist/Server/index.js", "scripts": {"generate-rpc": " node src/walletService/chainlist-rpc-finder.js", "build": "tsc", "start": "node dist/Server/index.js", "vercel-build": "npm run build", "start:dev": "clear && rm -rf dist && tsc && npm run build && node dist/Server/index.js", "dev": "clear && rm -rf dist && tsc && npm run build && node dist/Server/index.js", "test": "echo \"Error: no test specified\" && exit 1", "test:multi": "npm run build && node dist/test-multi-blockchain.js", "demo:multi": "npm run build && node dist/demo-multi-blockchain.js multi", "demo:single": "npm run build && node dist/demo-multi-blockchain.js single"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@biconomy/bundler": "^3.1.1-alpha.0", "@biconomy/core-types": "^3.1.1-alpha.0", "bip39": "^3.1.0", "bs58": "^6.0.0", "bs58check": "^4.0.0", "chalk": "^5.3.0", "ethereumjs-wallet": "^1.0.2", "ethers": "^5.7.2", "events": "^3.3.0", "express": "^4.19.2", "hdkey": "^2.1.0", "mongodb": "^6.1.0", "net": "^1.0.2", "nodemon": "^3.0.1", "userop": "^0.3.2"}, "devDependencies": {"@types/express": "^4.17.21", "@types/node": "^16.11.7", "ts-node": "^10.9.1", "typescript": "^4.5.4"}, "engines": {"node": ">=16"}}